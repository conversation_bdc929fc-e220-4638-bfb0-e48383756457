import { useState, useMemo } from 'react';

export type SortDirection = 'asc' | 'desc';
export type SortableValue = string | number | Date | null | undefined;

export interface SortConfig {
  key: string;
  direction: SortDirection;
}

export interface TableSortHook<T> {
  sortedData: T[];
  sortConfig: SortConfig | null;
  handleSort: (key: string) => void;
  getSortDirection: (key: string) => SortDirection | null;
}

const parseNumericValue = (value: SortableValue): number => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    // Remove currency symbols, commas, and other formatting
    const cleaned = value.replace(/[$,\s%+]/g, '');
    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

const parseDateValue = (value: SortableValue): Date => {
  if (value instanceof Date) return value;
  if (typeof value === 'string') {
    const parsed = new Date(value);
    return isNaN(parsed.getTime()) ? new Date(0) : parsed;
  }
  return new Date(0);
};

const parseStringValue = (value: SortableValue): string => {
  if (value === null || value === undefined) return '';
  return String(value).toLowerCase();
};

const getSortValue = (item: any, key: string): SortableValue => {
  const keys = key.split('.');
  let value = item;
  
  for (const k of keys) {
    if (value && typeof value === 'object') {
      value = value[k];
    } else {
      value = undefined;
      break;
    }
  }
  
  return value;
};

const compareValues = (a: SortableValue, b: SortableValue, key: string): number => {
  // Special handling for financial/numeric fields
  if (key.includes('Price') || key.includes('pl') || key.includes('PL') || 
      key.includes('balance') || key.includes('Balance') || key.includes('volume') ||
      key.includes('Volume') || key.includes('margin') || key.includes('Margin') ||
      key.includes('equity') || key.includes('Equity') || key.includes('commission') ||
      key.includes('swap') || key.includes('profitLoss') || key.includes('credit')) {
    const numA = parseNumericValue(a);
    const numB = parseNumericValue(b);
    return numA - numB;
  }
  
  // Special handling for time/date fields
  if (key.includes('Time') || key.includes('time') || key.includes('Date') || 
      key.includes('date') || key.includes('timestamp')) {
    const dateA = parseDateValue(a);
    const dateB = parseDateValue(b);
    return dateA.getTime() - dateB.getTime();
  }
  
  // Special handling for ID fields (numeric comparison if possible)
  if (key.includes('Id') || key.includes('ID')) {
    const numA = parseNumericValue(a);
    const numB = parseNumericValue(b);
    if (numA !== 0 && numB !== 0) {
      return numA - numB;
    }
  }
  
  // Default string comparison
  const strA = parseStringValue(a);
  const strB = parseStringValue(b);
  return strA.localeCompare(strB);
};

export function useTableSort<T>(data: T[], defaultSortKey?: string): TableSortHook<T> {
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(
    defaultSortKey ? { key: defaultSortKey, direction: 'asc' } : null
  );

  const sortedData = useMemo(() => {
    if (!sortConfig || !data.length) {
      return data;
    }

    const sorted = [...data].sort((a, b) => {
      const aValue = getSortValue(a, sortConfig.key);
      const bValue = getSortValue(b, sortConfig.key);
      
      const comparison = compareValues(aValue, bValue, sortConfig.key);
      
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [data, sortConfig]);

  const handleSort = (key: string) => {
    setSortConfig(prevConfig => {
      if (!prevConfig || prevConfig.key !== key) {
        return { key, direction: 'asc' };
      }
      
      if (prevConfig.direction === 'asc') {
        return { key, direction: 'desc' };
      }
      
      // Reset to no sorting
      return null;
    });
  };

  const getSortDirection = (key: string): SortDirection | null => {
    if (!sortConfig || sortConfig.key !== key) {
      return null;
    }
    return sortConfig.direction;
  };

  return {
    sortedData,
    sortConfig,
    handleSort,
    getSortDirection,
  };
}