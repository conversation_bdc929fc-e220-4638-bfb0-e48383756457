import React from 'react';
import { ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react';
import { SortDirection } from '../../hooks/useTableSort';

interface SortableTableHeaderProps {
  children: React.ReactNode;
  sortKey: string;
  onSort: (key: string) => void;
  sortDirection: SortDirection | null;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

const SortableTableHeader: React.FC<SortableTableHeaderProps> = ({
  children,
  sortKey,
  onSort,
  sortDirection,
  className = '',
  align = 'left'
}) => {
  const getSortIcon = () => {
    if (sortDirection === 'asc') {
      return <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4" />;
    } else if (sortDirection === 'desc') {
      return <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4" />;
    } else {
      return <ChevronsUpDown className="w-3 h-3 sm:w-4 sm:h-4 opacity-50" />;
    }
  };

  const getAlignmentClass = () => {
    switch (align) {
      case 'center':
        return 'text-center justify-center';
      case 'right':
        return 'text-right justify-end';
      default:
        return 'text-left justify-start';
    }
  };

  const getSortIndicatorClass = () => {
    if (sortDirection) {
      return sortDirection === 'asc' ? 'text-blue-400' : 'text-blue-400';
    }
    return 'text-gray-500 group-hover:text-gray-400';
  };

  return (
    <th
      className={`${getAlignmentClass()} p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400 ${className}`}
    >
      <button
        onClick={() => onSort(sortKey)}
        className={`group flex items-center space-x-1 hover:text-white transition-colors w-full ${getAlignmentClass()}`}
        title={`Sort by ${children} (${sortDirection === 'asc' ? 'ascending' : sortDirection === 'desc' ? 'descending' : 'unsorted'})`}
      >
        <span>{children}</span>
        <span className={getSortIndicatorClass()}>
          {getSortIcon()}
        </span>
      </button>
    </th>
  );
};

export default SortableTableHeader;