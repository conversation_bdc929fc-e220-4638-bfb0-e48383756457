import React, { useState, useMemo } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';
import { Users, AlertTriangle, AlertCircle } from 'lucide-react';
import { useTableSort } from '../hooks/useTableSort';
import SortableTableHeader from './ui/SortableTableHeader';

const TraderInfoPanel = () => {
  const { connectionStatus, traderAccounts } = useWebSocket();
  const [filters, setFilters] = useState({
    accountId: '',
    userGroup: 'all',
    status: 'not-normal'
  });

  const getMarginLevelColor = (marginLevel: number, marginCall: number, stopOut: number) => {
    if (marginLevel <= stopOut) {
      return 'text-red-400 bg-gradient-to-r from-red-500/30 to-red-600/20 border border-red-500/50';
    } else if (marginLevel <= marginCall) {
      return 'text-orange-400 bg-gradient-to-r from-orange-500/30 to-yellow-500/20 border border-orange-500/50';
    }
    return 'text-green-400 bg-gradient-to-r from-green-500/20 to-green-600/10 border border-green-500/30';
  };

  const getMarginLevelIcon = (marginLevel: number, marginCall: number, stopOut: number) => {
    if (marginLevel <= stopOut) {
      return <AlertCircle className="w-4 h-4 text-red-400 animate-pulse" />;
    } else if (marginLevel <= marginCall) {
      return <AlertTriangle className="w-4 h-4 text-orange-400 animate-pulse" />;
    }
    return null;
  };

  const getMarginStatus = (marginLevel: number, marginCall: number, stopOut: number) => {
    if (marginLevel <= stopOut) {
      return 'Stop Out';
    } else if (marginLevel <= marginCall) {
      return 'Margin Call';
    }
    return 'Normal';
  };

  const getStatusRowClasses = (marginLevel: number, marginCall: number, stopOut: number) => {
    if (marginLevel <= stopOut) {
      return 'border-b border-slate-700 hover:bg-slate-700/50 bg-red-500/5 animate-pulse';
    } else if (marginLevel <= marginCall) {
      return 'border-b border-slate-700 hover:bg-slate-700/50 bg-orange-500/5';
    }
    return 'border-b border-slate-700 hover:bg-slate-700/50';
  };

  // Filter trader accounts based on current filters
  const filteredAccounts = useMemo(() => {
    return traderAccounts.filter(account => {
      // Account ID filter
      if (filters.accountId && !account.accountId.toLowerCase().includes(filters.accountId.toLowerCase())) {
        return false;
      }

      // User Group filter
      if (filters.userGroup !== 'all') {
        if (filters.userGroup === 'premium' && account.userGroup !== 'Premium Clients') return false;
        if (filters.userGroup === 'standard' && account.userGroup !== 'Standard Clients') return false;
      }

      // Status filter
      if (filters.status !== 'all') {
        const status = getMarginStatus(account.marginLevel, account.marginCall, account.stopOut);
        if (filters.status === 'normal' && status !== 'Normal') return false;
        if (filters.status === 'not-normal' && status === 'Normal') return false;
        if (filters.status === 'margin-call' && status !== 'Margin Call') return false;
        if (filters.status === 'stop-out' && status !== 'Stop Out') return false;
      }

      return true;
    });
  }, [traderAccounts, filters]);

  // Add table sorting functionality
  const { sortedData: sortedAccounts, handleSort, getSortDirection } = useTableSort(filteredAccounts, 'accountId');

  return (
    <div className="bg-slate-800 rounded-lg p-2 sm:p-4 border border-slate-700 w-full h-full flex flex-col">
      {/* Header with Inline Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 flex-shrink-0 mb-4">
        {/* Title and Account Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="w-4 sm:w-5 h-4 sm:h-5 text-blue-400" />
            <h2 className="text-base sm:text-lg font-semibold text-white">Trader Accounts</h2>
          </div>
          <div className="text-xs sm:text-sm text-gray-400 lg:hidden">
            {sortedAccounts.length} of {traderAccounts.length} account(s)
          </div>
        </div>

        {/* Inline Filters */}
        <div className="flex flex-wrap gap-2 lg:gap-3 items-end">
          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">Account ID</label>
            <input
              type="text"
              placeholder="Search account..."
              value={filters.accountId}
              onChange={(e) => setFilters({ ...filters, accountId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-28">
            <label className="block text-xs font-medium text-gray-400 mb-1">User Group</label>
            <select
              value={filters.userGroup}
              onChange={(e) => setFilters({ ...filters, userGroup: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Groups</option>
              <option value="premium">Premium Clients</option>
              <option value="standard">Standard Clients</option>
            </select>
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-28">
            <label className="block text-xs font-medium text-gray-400 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="normal">Normal</option>
              <option value="not-normal">Not Normal</option>
              <option value="margin-call">Margin Call</option>
              <option value="stop-out">Stop Out</option>
            </select>
          </div>

          <div className="hidden lg:block text-xs text-gray-400 self-end pb-1.5">
            {sortedAccounts.length} of {traderAccounts.length} account(s)
          </div>
        </div>
      </div>
      
      {sortedAccounts.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <p className="text-gray-400 text-sm">No trader accounts match the current filters.</p>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto border border-slate-600 rounded-lg">
          <table className="w-full text-xs sm:text-sm">
            <thead className="sticky top-0 bg-slate-800 z-10">
              <tr className="border-b border-slate-600">
                <SortableTableHeader sortKey="accountId" onSort={handleSort} sortDirection={getSortDirection('accountId')}>
                  Account ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="userGroup" onSort={handleSort} sortDirection={getSortDirection('userGroup')}>
                  User Group
                </SortableTableHeader>
                <SortableTableHeader sortKey="balance" onSort={handleSort} sortDirection={getSortDirection('balance')} align="right">
                  Balance
                </SortableTableHeader>
                <SortableTableHeader sortKey="creditLimit" onSort={handleSort} sortDirection={getSortDirection('creditLimit')} align="right">
                  Credit Limit
                </SortableTableHeader>
                <SortableTableHeader sortKey="floatingPnL" onSort={handleSort} sortDirection={getSortDirection('floatingPnL')} align="right">
                  Floating P/L
                </SortableTableHeader>
                <SortableTableHeader sortKey="equity" onSort={handleSort} sortDirection={getSortDirection('equity')} align="right">
                  Equity
                </SortableTableHeader>
                <SortableTableHeader sortKey="currency" onSort={handleSort} sortDirection={getSortDirection('currency')} align="center">
                  Currency
                </SortableTableHeader>
                <SortableTableHeader sortKey="usedMargin" onSort={handleSort} sortDirection={getSortDirection('usedMargin')} align="right">
                  Used Margin
                </SortableTableHeader>
                <SortableTableHeader sortKey="marginLevel" onSort={handleSort} sortDirection={getSortDirection('marginLevel')} align="right">
                  Margin Level
                </SortableTableHeader>
                <th className="text-center px-2 sm:px-3 py-2 text-xs font-medium text-gray-400">Status</th>
              </tr>
            </thead>
            <tbody>
              {sortedAccounts.map((account) => (
                <tr key={account.accountId} className={getStatusRowClasses(account.marginLevel, account.marginCall, account.stopOut)}>
                  <td className="px-2 sm:px-3 py-2 font-medium text-white">{account.accountId}</td>
                  <td className="px-2 sm:px-3 py-2 text-white">
                    <span className={`px-2 py-1 rounded text-xs ${
                      account.userGroup === 'Premium Clients' 
                        ? 'bg-blue-500/20 text-blue-400' 
                        : 'bg-gray-500/20 text-gray-400'
                    }`}>
                      {account.userGroup}
                    </span>
                  </td>
                  <td className="px-2 sm:px-3 py-2 font-mono text-right text-white">
                    ${account.balance.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                  <td className="px-2 sm:px-3 py-2 font-mono text-right text-blue-300">
                    ${account.creditLimit.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                  <td className="px-2 sm:px-3 py-2 font-mono text-right">
                    <span className={`${account.floatingPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {account.floatingPnL >= 0 ? '+' : ''}${account.floatingPnL.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                  </td>
                  <td className="px-2 sm:px-3 py-2 font-mono text-right text-white">
                    ${account.equity.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                  <td className="px-2 sm:px-3 py-2 text-center">
                    <span className="px-2 py-1 rounded text-xs bg-gray-500/20 text-gray-300 font-medium">
                      {account.currency}
                    </span>
                  </td>
                  <td className="px-2 sm:px-3 py-2 font-mono text-right text-orange-300">
                    ${account.usedMargin.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </td>
                  <td className="px-2 sm:px-3 py-2 font-mono text-right">
                    <span className={`px-2 py-1 rounded ${getMarginLevelColor(account.marginLevel, account.marginCall, account.stopOut)}`}>
                      {account.marginLevel.toFixed(1)}%
                    </span>
                  </td>
                  <td className="px-2 sm:px-3 py-2 text-center">
                    <div className="flex items-center justify-center space-x-1">
                      {getMarginLevelIcon(account.marginLevel, account.marginCall, account.stopOut)}
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        account.marginLevel <= account.stopOut 
                          ? 'text-red-300 bg-gradient-to-r from-red-500/30 to-red-600/20 border border-red-500/50 animate-pulse' 
                          : account.marginLevel <= account.marginCall 
                          ? 'text-orange-300 bg-gradient-to-r from-orange-500/30 to-yellow-500/20 border border-orange-500/50' 
                          : 'text-green-300 bg-gradient-to-r from-green-500/20 to-green-600/10 border border-green-500/30'
                      }`}>
                        {getMarginStatus(account.marginLevel, account.marginCall, account.stopOut)}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default TraderInfoPanel; 