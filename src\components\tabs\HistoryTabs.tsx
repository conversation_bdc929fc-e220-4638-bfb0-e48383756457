import React, { useState, useMemo } from 'react';
import { Search, Calendar, Edit, Trash2 } from 'lucide-react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import EditPositionHistoryModal from '../modals/EditPositionHistoryModal';
import EditOrderHistoryModal from '../modals/EditOrderHistoryModal';
import EditDealHistoryModal from '../modals/EditDealHistoryModal';
import DeleteHistoryRecordModal from '../modals/DeleteHistoryRecordModal';
import { formatPrice, formatPriceOrFallback } from '../../utils/priceFormatting';
import { useTableSort } from '../../hooks/useTableSort';
import SortableTableHeader from '../ui/SortableTableHeader';

interface HistoryTabsProps {
  activeTab: string;
}

const HistoryTabs = ({ activeTab }: HistoryTabsProps) => {
  const { positionHistory, orderHistory, dealHistory, deletePositionHistory, deleteOrderHistory, deleteDealHistory, traderAccounts } = useWebSocket();
  const [editingPosition, setEditingPosition] = useState<any>(null);
  const [editingOrder, setEditingOrder] = useState<any>(null);
  const [editingDeal, setEditingDeal] = useState<any>(null);
  const [deletingRecord, setDeletingRecord] = useState<any>(null);
  const [deleteRecordType, setDeleteRecordType] = useState<'position' | 'order' | 'deal'>('position');
  const [filters, setFilters] = useState({
    userGroup: 'all',
    accountId: '',
    symbol: '',
    dateFrom: '',
    dateTo: ''
  });

  const getTabTitle = () => {
    switch (activeTab) {
      case 'position-history':
        return 'Position History';
      case 'order-history':
        return 'Order History';
      case 'deal-history':
        return 'Deal History';
      default:
        return 'History';
    }
  };

  const getHistoryData = () => {
    switch (activeTab) {
      case 'position-history':
        return positionHistory;
      case 'order-history':
        return orderHistory;
      case 'deal-history':
        return dealHistory;
      default:
        return [];
    }
  };

  const filteredHistory = useMemo(() => {
    const data = getHistoryData();
    return data.filter(item => {
      // User Group filter - look up the actual user group from traderAccounts
      if (filters.userGroup !== 'all') {
        const account = traderAccounts.find(acc => acc.accountId === item.accountId);
        if (account) {
          if (filters.userGroup === 'premium' && account.userGroup !== 'Premium Clients') return false;
          if (filters.userGroup === 'standard' && account.userGroup !== 'Standard Clients') return false;
        }
      }

      // Account ID filter
      if (filters.accountId && !item.accountId.toLowerCase().includes(filters.accountId.toLowerCase())) {
        return false;
      }

      // Symbol filter
      if (filters.symbol && !item.symbol.toLowerCase().includes(filters.symbol.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [positionHistory, orderHistory, dealHistory, activeTab, filters, traderAccounts]);

  // Add table sorting functionality - different default sort for each tab
  const getDefaultSortKey = () => {
    switch (activeTab) {
      case 'position-history':
        return 'finalPL';
      case 'order-history':
        return 'placementTime';
      case 'deal-history':
        return 'executionTime';
      default:
        return 'accountId';
    }
  };

  const { sortedData: sortedHistory, handleSort, getSortDirection } = useTableSort(filteredHistory, getDefaultSortKey());

  const handleDeleteRecord = async (recordId: string): Promise<void> => {
    if (deleteRecordType === 'position') {
      await deletePositionHistory(recordId);
    } else if (deleteRecordType === 'order') {
      await deleteOrderHistory(recordId);
    } else if (deleteRecordType === 'deal') {
      await deleteDealHistory(recordId);
    }
  };

  const handleDeleteClick = (record: any, type: 'position' | 'order' | 'deal') => {
    setDeletingRecord(record);
    setDeleteRecordType(type);
  };

  const renderHistoryTable = () => {
    if (sortedHistory.length === 0) {
      return (
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-6 h-6 text-gray-400" />
            </div>
            <p className="text-gray-400 text-lg">No records found.</p>
            <p className="text-gray-500 text-sm mt-2">
              {activeTab === 'deal-history' 
                ? 'Historical deals will appear here. Use the Edit button to make corrections with audit trail.'
                : 'Historical records will appear here based on your filter criteria.'
              }
            </p>
          </div>
        </div>
      );
    }

    if (activeTab === 'position-history') {
      return (
        <div className="h-full overflow-y-auto border border-slate-600 rounded-lg">
          <table className="w-full">
            <thead className="sticky top-0 bg-slate-800 z-10">
              <tr className="border-b border-slate-600">
                <SortableTableHeader sortKey="accountId" onSort={handleSort} sortDirection={getSortDirection('accountId')}>
                  Account ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="positionId" onSort={handleSort} sortDirection={getSortDirection('positionId')}>
                  Position ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="symbol" onSort={handleSort} sortDirection={getSortDirection('symbol')}>
                  Symbol
                </SortableTableHeader>
                <SortableTableHeader sortKey="type" onSort={handleSort} sortDirection={getSortDirection('type')}>
                  Type
                </SortableTableHeader>
                <SortableTableHeader sortKey="volume" onSort={handleSort} sortDirection={getSortDirection('volume')}>
                  Volume
                </SortableTableHeader>
                <SortableTableHeader sortKey="openPrice" onSort={handleSort} sortDirection={getSortDirection('openPrice')}>
                  Open Price
                </SortableTableHeader>
                <SortableTableHeader sortKey="closePrice" onSort={handleSort} sortDirection={getSortDirection('closePrice')}>
                  Close Price
                </SortableTableHeader>
                <SortableTableHeader sortKey="finalPL" onSort={handleSort} sortDirection={getSortDirection('finalPL')}>
                  Final P/L
                </SortableTableHeader>
                <SortableTableHeader sortKey="openTime" onSort={handleSort} sortDirection={getSortDirection('openTime')}>
                  Open Time
                </SortableTableHeader>
                <SortableTableHeader sortKey="closeTime" onSort={handleSort} sortDirection={getSortDirection('closeTime')}>
                  Close Time
                </SortableTableHeader>
                <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedHistory.map((position, index) => (
                <tr key={`${position.positionId}-${index}`} className="border-b border-slate-700 hover:bg-slate-700/50">
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{position.accountId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{position.positionId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-medium">{position.symbol}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm">
                    <span className={`px-2 py-1 rounded text-xs ${
                      position.type === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}>
                      {position.type}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{position.closedVolume || position.volume}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(position.openPrice, position.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(position.closePrice, position.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm font-mono">
                    <span className={position.finalPL >= 0 ? 'text-green-400' : 'text-red-400'}>
                      {position.finalPL >= 0 ? '+' : ''}${position.finalPL.toFixed(2)}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{position.openTime}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{position.closeTime}</td>
                  <td className="p-2 sm:p-3">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <button
                        onClick={() => setEditingPosition(position)}
                        className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                        title="Edit Position"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(position, 'position')}
                        className="p-1 text-red-400 hover:text-red-300 transition-colors"
                        title="Delete Position Record"
                      >
                        <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }

    if (activeTab === 'order-history') {
      return (
        <div className="h-full overflow-y-auto border border-slate-600 rounded-lg">
          <table className="w-full">
            <thead className="sticky top-0 bg-slate-800 z-10">
              <tr className="border-b border-slate-600">
                <SortableTableHeader sortKey="accountId" onSort={handleSort} sortDirection={getSortDirection('accountId')}>
                  Account ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="orderId" onSort={handleSort} sortDirection={getSortDirection('orderId')}>
                  Order ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="symbol" onSort={handleSort} sortDirection={getSortDirection('symbol')}>
                  Symbol
                </SortableTableHeader>
                <SortableTableHeader sortKey="type" onSort={handleSort} sortDirection={getSortDirection('type')}>
                  Type
                </SortableTableHeader>
                <SortableTableHeader sortKey="volume" onSort={handleSort} sortDirection={getSortDirection('volume')}>
                  Volume
                </SortableTableHeader>
                <SortableTableHeader sortKey="orderPrice" onSort={handleSort} sortDirection={getSortDirection('orderPrice')}>
                  Order Price
                </SortableTableHeader>
                <SortableTableHeader sortKey="placementTime" onSort={handleSort} sortDirection={getSortDirection('placementTime')}>
                  Placement Time
                </SortableTableHeader>
                <SortableTableHeader sortKey="cancelTime" onSort={handleSort} sortDirection={getSortDirection('cancelTime')}>
                  Cancel Time
                </SortableTableHeader>
                <SortableTableHeader sortKey="status" onSort={handleSort} sortDirection={getSortDirection('status')}>
                  Status
                </SortableTableHeader>
                <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedHistory.map((order, index) => (
                <tr key={`${order.orderId}-${index}`} className="border-b border-slate-700 hover:bg-slate-700/50">
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{order.accountId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{order.orderId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-medium">{order.symbol}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm">
                    <span className="px-2 py-1 rounded text-xs bg-yellow-500/20 text-yellow-400">
                      {order.type}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{order.volume}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(order.orderPrice, order.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{order.placementTime}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{order.cancelTime}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm">
                    <span className="px-2 py-1 rounded text-xs bg-red-500/20 text-red-400">
                      {order.status}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <button
                        onClick={() => setEditingOrder(order)}
                        className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                        title="Edit Order"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(order, 'order')}
                        className="p-1 text-red-400 hover:text-red-300 transition-colors"
                        title="Delete Order Record"
                      >
                        <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }

    if (activeTab === 'deal-history') {
      return (
        <div className="h-full overflow-y-auto border border-slate-600 rounded-lg">
          <table className="w-full">
            <thead className="sticky top-0 bg-slate-800 z-10">
              <tr className="border-b border-slate-600">
                <SortableTableHeader sortKey="executionTime" onSort={handleSort} sortDirection={getSortDirection('executionTime')}>
                  Execution Time
                </SortableTableHeader>
                <SortableTableHeader sortKey="dealId" onSort={handleSort} sortDirection={getSortDirection('dealId')}>
                  Deal ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="accountId" onSort={handleSort} sortDirection={getSortDirection('accountId')}>
                  Account ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="symbol" onSort={handleSort} sortDirection={getSortDirection('symbol')}>
                  Symbol
                </SortableTableHeader>
                <SortableTableHeader sortKey="type" onSort={handleSort} sortDirection={getSortDirection('type')}>
                  Type
                </SortableTableHeader>
                <SortableTableHeader sortKey="direction" onSort={handleSort} sortDirection={getSortDirection('direction')}>
                  Direction
                </SortableTableHeader>
                <SortableTableHeader sortKey="volume" onSort={handleSort} sortDirection={getSortDirection('volume')}>
                  Volume
                </SortableTableHeader>
                <SortableTableHeader sortKey="executionPrice" onSort={handleSort} sortDirection={getSortDirection('executionPrice')}>
                  Execution Price
                </SortableTableHeader>
                <SortableTableHeader sortKey="orderId" onSort={handleSort} sortDirection={getSortDirection('orderId')}>
                  Order ID
                </SortableTableHeader>
                <SortableTableHeader sortKey="commission" onSort={handleSort} sortDirection={getSortDirection('commission')} align="right">
                  Commission
                </SortableTableHeader>
                <SortableTableHeader sortKey="swap" onSort={handleSort} sortDirection={getSortDirection('swap')} align="right">
                  Swap
                </SortableTableHeader>
                <SortableTableHeader sortKey="profitLoss" onSort={handleSort} sortDirection={getSortDirection('profitLoss')} align="right">
                  Profit/Loss
                </SortableTableHeader>
                <SortableTableHeader sortKey="balance" onSort={handleSort} sortDirection={getSortDirection('balance')} align="right">
                  Balance
                </SortableTableHeader>
                <SortableTableHeader sortKey="comment" onSort={handleSort} sortDirection={getSortDirection('comment')}>
                  Comment
                </SortableTableHeader>
                <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sortedHistory.length === 0 ? (
                <tr>
                  <td colSpan={15} className="text-center py-8 text-gray-400">
                    No deal records found.
                  </td>
                </tr>
              ) : (
                sortedHistory.map((deal, index) => (
                  <tr key={`${deal.dealId}-${index}`} className="border-b border-slate-700 hover:bg-slate-700/50">
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{deal.executionTime}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">
                      {deal.dealId.startsWith('DEAL') ? deal.dealId : `DEAL${deal.dealId}`}
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{deal.accountId}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-medium">{deal.symbol || '—'}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm">
                      <span className={`px-2 py-1 rounded text-xs ${
                        deal.type === 'balance' ? 'bg-blue-500/20 text-blue-400' :
                        deal.type === 'buy' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                      }`}>
                        {deal.type}
                      </span>
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm">
                      {deal.direction ? (
                        <span className={`px-2 py-1 rounded text-xs ${
                          deal.direction === 'in' ? 'bg-purple-500/20 text-purple-400' : 'bg-orange-500/20 text-orange-400'
                        }`}>
                          {deal.direction}
                        </span>
                      ) : '—'}
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{deal.volume || '—'}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPriceOrFallback(deal.executionPrice, deal.symbol || 'EURUSD')}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{deal.orderId || '—'}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">
                      <span className={deal.commission >= 0 ? 'text-green-400' : 'text-red-400'}>
                        {deal.commission >= 0 ? '+' : ''}${deal.commission.toFixed(2)}
                      </span>
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">
                      <span className={deal.swap >= 0 ? 'text-green-400' : 'text-red-400'}>
                        {deal.swap >= 0 ? '+' : ''}${deal.swap.toFixed(2)}
                      </span>
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm font-mono">
                      <span className={deal.profitLoss >= 0 ? 'text-green-400' : 'text-red-400'}>
                        {deal.profitLoss >= 0 ? '+' : ''}${deal.profitLoss.toFixed(2)}
                      </span>
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono font-bold">
                      ${deal.balance.toFixed(2)}
                    </td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{deal.comment || '—'}</td>
                    <td className="p-2 sm:p-3 text-xs sm:text-sm">
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <button
                          onClick={() => setEditingDeal(deal)}
                          className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                          title="Edit Deal Record"
                        >
                          <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(deal, 'deal')}
                          className="p-1 text-red-400 hover:text-red-300 transition-colors"
                          title="Delete Deal Record"
                        >
                          <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 flex-shrink-0 mb-4">
        {/* Title */}
        <div className="flex items-center">
          <h3 className="text-lg font-semibold text-white">{getTabTitle()}</h3>
        </div>

        {/* Inline Filters */}
        <div className="flex flex-wrap gap-2 lg:gap-3">
          <div className="min-w-0 flex-1 lg:flex-none lg:w-28">
            <label className="block text-xs font-medium text-gray-400 mb-1">User Group</label>
            <select
              value={filters.userGroup}
              onChange={(e) => setFilters({ ...filters, userGroup: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">All Groups</option>
              <option value="premium">Premium Clients</option>
              <option value="standard">Standard Clients</option>
            </select>
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">Account ID</label>
            <input
              type="text"
              placeholder="Search account..."
              value={filters.accountId}
              onChange={(e) => setFilters({ ...filters, accountId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-28">
            <label className="block text-xs font-medium text-gray-400 mb-1">Symbol</label>
            <input
              type="text"
              placeholder="e.g. EURUSD"
              value={filters.symbol}
              onChange={(e) => setFilters({ ...filters, symbol: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">Date From</label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => setFilters({ ...filters, dateFrom: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">Date To</label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => setFilters({ ...filters, dateTo: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Results - Fill remaining height */}
      <div className="flex-1 overflow-hidden">
        {renderHistoryTable()}
      </div>

      {/* Edit Modals */}
      {editingPosition && (
        <EditPositionHistoryModal
          isOpen={!!editingPosition}
          position={editingPosition}
          onClose={() => setEditingPosition(null)}
        />
      )}

      {editingOrder && (
        <EditOrderHistoryModal
          isOpen={!!editingOrder}
          order={editingOrder}
          onClose={() => setEditingOrder(null)}
        />
      )}

      {editingDeal && (
        <EditDealHistoryModal
          isOpen={!!editingDeal}
          deal={editingDeal}
          onClose={() => setEditingDeal(null)}
        />
      )}

      {/* Delete Modal */}
      {deletingRecord && (
        <DeleteHistoryRecordModal
          isOpen={!!deletingRecord}
          record={deletingRecord}
          recordType={deleteRecordType}
          onClose={() => {
            setDeletingRecord(null);
            setDeleteRecordType('position');
          }}
          onConfirm={handleDeleteRecord}
        />
      )}
    </div>
  );
};

export default HistoryTabs;
