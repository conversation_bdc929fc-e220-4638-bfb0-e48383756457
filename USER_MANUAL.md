# OTX Dealer Terminal Pro - User Manual

## Table of Contents

1. [Getting Started](#getting-started)
2. [Core Features Documentation](#core-features-documentation)
3. [Trading Operations](#trading-operations)
4. [Data Management](#data-management)
5. [Keyboard Shortcuts and Efficiency Tips](#keyboard-shortcuts-and-efficiency-tips)
6. [Security and Compliance](#security-and-compliance)
7. [Troubleshooting](#troubleshooting)

---

## Getting Started

### Login Process

The OTX Dealer Terminal Pro uses a secure authentication system with optional CAPTCHA verification for enhanced security.

#### Demo Credentials
- **Username**: `dealer01`
- **Password**: `password123`

#### Login Steps
1. Navigate to the login page
2. Enter your dealer credentials
3. Complete CAPTCHA verification if enabled
4. Click "Sign In" to access the trading terminal

#### CAPTCHA Verification
When enabled, the system uses reCAPTCHA for additional security. Simply check the "I'm not a robot" box and complete any additional verification steps if prompted.

### Dashboard Overview and Navigation

The OTX Dealer Terminal Pro features a professional 4-panel layout designed for efficient trading operations:

#### Main Interface Layout
```
┌─────────────────────┬─────────────────────┐
│   Dealer Analytics  │    Price Quotes     │
│      Panel          │       Panel         │
├─────────────────────┼─────────────────────┤
│    Trader Info      │    Main Data        │
│      Panel          │      Panel          │
└─────────────────────┴─────────────────────┘
```

#### Panel Overview
- **Top-Left**: Dealer Analytics Panel - Performance metrics and alerts
- **Top-Right**: Price Quotes Panel - Real-time market data and trading controls
- **Bottom-Left**: Trader Info Panel - Account information and balances
- **Bottom-Right**: Main Data Panel - Positions, orders, and historical data

### Initial Setup and Configuration

Upon first login, verify:
1. **Connection Status**: Green indicator shows active WebSocket connection
2. **Trading Controls**: Ensure trading is enabled for required symbols
3. **User Group Settings**: Confirm proper client tier assignment (Premium/Standard)
4. **Display Preferences**: Tables are sorted by default by most recent activity

---

## Core Features Documentation

### Dealer Analytics Panel

The Dealer Analytics Panel provides comprehensive performance tracking and risk management tools.

#### Key Metrics Display
- **Total P/L**: Aggregate profit/loss across all positions
- **Daily Volume**: Trading volume for current session
- **Active Positions**: Number of open positions
- **Pending Orders**: Count of pending limit and stop orders
- **Risk Alerts**: Real-time notifications for margin calls or stop-outs

#### Alert System
The panel displays color-coded alerts:
- **Green**: Normal trading conditions
- **Yellow**: Approaching margin limits
- **Red**: Critical margin call or stop-out conditions

### Price Quotes Panel

The Price Quotes Panel displays real-time market data with integrated trading controls.

#### Features
- **Symbol List**: All available trading instruments
- **Bid/Ask Prices**: Real-time pricing with 5-decimal precision for forex pairs
- **Spread Display**: Current bid-ask spread for each symbol
- **Price Movement**: Color-coded price changes (green=up, red=down)
- **Trading Controls**: Individual symbol enable/disable toggles

#### Symbol-Specific Formatting
- **Forex Pairs**: 5 decimal places (e.g., 1.23456)
- **Gold (XAUUSD)**: 2 decimal places (e.g., 1234.56)
- **JPY Pairs**: 3 decimal places (e.g., 123.456)
- **Cryptocurrencies**: 1 decimal place (e.g., 12345.6)

#### Trading Control Functions
- **Enable/Disable Trading**: Toggle trading availability per symbol
- **Quick Trade Access**: Double-click any quote to open New Position modal
- **Market Status**: Real-time connection and market status indicators

### Trader Info Panel

The Trader Info Panel provides comprehensive account monitoring and management tools.

#### Account Information Display
- **Account ID**: Unique trader identifier
- **Account Balance**: Current account equity
- **Margin Level**: Available margin percentage
- **Free Margin**: Available funds for new positions
- **Equity**: Total account value including floating P/L
- **Used Margin**: Margin currently allocated to open positions

#### Risk Management Indicators
- **Margin Call Level**: Warning threshold display
- **Stop Out Level**: Automatic position closure threshold
- **Account Status**: Normal, Margin Call, or Stop Out status

#### User Group Management
- **Client Tier**: Premium or Standard classification
- **Trading Permissions**: Symbol-specific trading rights
- **Account Restrictions**: Any applied trading limitations

### Main Data Panel

The Main Data Panel features a tabbed interface for comprehensive trading data management.

#### Tab Structure
1. **Positions**: Currently open trading positions
2. **Orders**: Pending limit and stop orders
3. **Position History**: Historical position records
4. **Order History**: Historical order records
5. **Deal History**: Complete transaction history
6. **Audit Log**: Detailed activity tracking

#### Table Features
- **Sortable Columns**: Click any column header to sort
- **Right-Click Menus**: Context-sensitive actions
- **Row Selection**: Single or multiple row selection
- **Real-time Updates**: Live data synchronization

---

## Trading Operations

### Creating New Positions

#### Method 1: Keyboard Shortcut
- Press `Ctrl+N` to open the New Position modal

#### Method 2: Double-Click Quote
- Double-click any symbol in the Price Quotes panel

#### Method 3: Right-Click Menu
- Right-click in the Positions tab and select "New Position"

#### New Position Modal Fields
1. **Symbol**: Select trading instrument from dropdown
2. **Action**: Choose BUY or SELL
3. **Volume**: Position size in lots (e.g., 0.01, 0.10, 1.00)
4. **Order Type**: MARKET (immediate execution)
5. **Stop Loss**: Optional risk management level
6. **Take Profit**: Optional profit target level
7. **Comment**: Optional position notes

#### Position Creation Process
1. Fill required fields (Symbol, Action, Volume)
2. Set optional Stop Loss and Take Profit levels
3. Click "Create Position" to execute
4. Confirm execution in the positions table
5. Monitor real-time P/L updates

### Creating Orders

#### Order Types Available
- **LIMIT Order**: Execute at specified price or better
- **STOP Order**: Execute when price reaches trigger level

#### New Order Modal Fields
1. **Symbol**: Trading instrument selection
2. **Action**: BUY or SELL direction
3. **Volume**: Order size in lots
4. **Order Type**: LIMIT or STOP
5. **Price**: Execution price target
6. **Stop Loss**: Optional risk management
7. **Take Profit**: Optional profit target
8. **Expiration**: Order validity period
9. **Comment**: Optional order notes

#### Order Management
- **Pending Orders**: Display in Orders tab with status
- **Order Modification**: Double-click to edit parameters
- **Order Cancellation**: Right-click and select "Cancel"
- **Order Execution**: Automatic conversion to position when triggered

### Modifying Existing Positions

#### Position Modification Methods
1. **Double-Click**: Open Edit Position modal
2. **Right-Click Menu**: Select "Modify Position"
3. **Direct Field Edit**: Click and edit specific fields

#### Modifiable Parameters
- **Stop Loss**: Risk management level adjustment
- **Take Profit**: Profit target modification
- **Volume**: Partial position closure
- **Comment**: Position notes update

#### Modification Process
1. Select position to modify
2. Open Edit Position modal
3. Adjust desired parameters
4. Click "Update Position" to apply changes
5. Confirm changes in audit log

### Closing Positions

#### Full Position Closure
1. Right-click position in Positions tab
2. Select "Close Position"
3. Confirm closure in dialog
4. Verify P/L realization in Deal History

#### Partial Position Closure
1. Double-click position to open Edit modal
2. Reduce Volume field to desired amount
3. Click "Update Position"
4. Original position splits into closed and remaining portions

### Understanding Trading Controls

#### Symbol-Level Controls
- **Trading Enabled**: Green indicator allows new positions
- **Trading Disabled**: Red indicator prevents new positions
- **Existing Positions**: Remain active regardless of trading status

#### Global Trading Controls
- **Master Enable/Disable**: Affects all symbols simultaneously
- **Emergency Stop**: Immediate trading halt capability
- **Maintenance Mode**: Temporary trading suspension

---

## Data Management

### Viewing and Filtering Position History

#### Position History Features
- **Complete Record**: All closed positions with full details
- **Editable Records**: Historical data modification capability
- **Advanced Filtering**: Multiple criteria selection
- **Sortable Columns**: Flexible data organization

#### Filtering Options
1. **Date Range**: Specify start and end dates
2. **Symbol Filter**: Select specific trading instruments
3. **Account Filter**: Filter by trader account ID
4. **User Group**: Premium or Standard client filter
5. **P/L Range**: Profit/loss amount filtering

#### Available Data Columns
- **Position ID**: Unique identifier
- **Symbol**: Trading instrument
- **Action**: BUY or SELL
- **Volume**: Position size
- **Open Price**: Entry price
- **Close Price**: Exit price
- **Open Time**: Position creation timestamp
- **Close Time**: Position closure timestamp
- **P/L**: Realized profit/loss
- **Commission**: Trading fees
- **Swap**: Overnight financing charges
- **Comment**: Position notes

### Managing Order History

#### Order History Overview
The Order History tab maintains complete records of all order activity, including:
- **Created Orders**: Initial order placement
- **Modified Orders**: Parameter changes
- **Canceled Orders**: Manual cancellations
- **Executed Orders**: Successful order fills
- **Expired Orders**: Time-based expirations

#### Order Status Tracking
- **PENDING**: Order awaiting execution
- **FILLED**: Order successfully executed
- **CANCELED**: Order manually canceled
- **EXPIRED**: Order expired due to time limit
- **REJECTED**: Order rejected due to trading restrictions

#### Filtering and Search
Use the same filtering system as Position History:
1. Date range selection
2. Symbol filtering
3. Order type filtering (LIMIT/STOP)
4. Status filtering
5. Account ID filtering

### Deal History Management

#### Deal History Purpose
The Deal History tab provides comprehensive transaction records for:
- **Regulatory Compliance**: Complete audit trail
- **Performance Analysis**: Trading pattern evaluation
- **Reconciliation**: Account balance verification
- **Reporting**: Management and client reporting

#### Deal Record Details
Each deal record contains:
- **Deal ID**: Unique transaction identifier
- **Position ID**: Related position reference
- **Symbol**: Trading instrument
- **Action**: BUY or SELL
- **Volume**: Transaction volume
- **Price**: Execution price
- **Time**: Precise execution timestamp
- **P/L**: Transaction profit/loss
- **Balance**: Account balance after transaction
- **Comment**: Transaction notes

#### Advanced Deal Analysis
- **Trade Matching**: Link opening and closing transactions
- **Performance Metrics**: Win/loss ratios and averages
- **Time Analysis**: Trading pattern identification
- **Symbol Performance**: Instrument-specific results

### Using the Audit Log for Compliance

#### Audit Log Purpose
The Audit Log provides comprehensive activity tracking for:
- **Regulatory Compliance**: Complete user activity trail
- **Security Monitoring**: Unauthorized access detection
- **Performance Review**: User behavior analysis
- **System Debugging**: Technical issue resolution

#### Audit Entry Types
1. **Authentication Events**: Login/logout activities
2. **Trading Actions**: Position and order operations
3. **Data Modifications**: Record edits and updates
4. **System Events**: Connection and status changes
5. **Administrative Actions**: System configuration changes

#### Audit Log Features
- **Expandable Entries**: Click to view detailed information
- **User Tracking**: Complete user session history
- **Timestamp Precision**: Millisecond-accurate timing
- **Change Tracking**: Before/after value comparison
- **IP Address Logging**: Session security monitoring

#### Compliance Best Practices
1. **Regular Review**: Monitor audit logs daily
2. **Anomaly Detection**: Identify unusual activity patterns
3. **Record Retention**: Maintain logs per regulatory requirements
4. **Access Control**: Restrict audit log access to authorized personnel
5. **Backup Procedures**: Ensure audit log data protection

### Editing Historical Records

#### When to Edit Historical Data
Historical record editing should only be performed for:
- **Data Correction**: Fix erroneous entries
- **Compliance Updates**: Add required regulatory information
- **Comment Updates**: Enhance record documentation
- **Administrative Adjustments**: Authorized management changes

#### Edit Process
1. Navigate to appropriate history tab
2. Double-click record to open edit modal
3. Modify permitted fields
4. Add edit justification in comments
5. Save changes to create audit trail entry

#### Edit Restrictions
- **Financial Data**: P/L and pricing generally protected
- **System Data**: Automatic timestamps cannot be modified
- **Regulatory Fields**: Some fields may be locked per compliance rules
- **User Permissions**: Edit rights vary by user role

#### Audit Trail for Edits
Every historical record edit creates:
- **Original Value Record**: Pre-edit data preservation
- **Change Description**: What was modified
- **User Identification**: Who made the change
- **Timestamp**: When change was made
- **Justification**: Why change was necessary

---

## Keyboard Shortcuts and Efficiency Tips

### Primary Keyboard Shortcuts

#### Modal Operations
- **ESC**: Close any open modal without saving
- **Enter**: Confirm action in focused modal button
- **Tab**: Navigate between form fields

#### Trading Operations
- **Ctrl+N**: Open New Position modal from anywhere
- **Ctrl+Shift+N**: Open New Order modal
- **F5**: Refresh all real-time data

#### Navigation Shortcuts
- **Ctrl+1**: Focus on Dealer Analytics Panel
- **Ctrl+2**: Focus on Price Quotes Panel
- **Ctrl+3**: Focus on Trader Info Panel
- **Ctrl+4**: Focus on Main Data Panel

### Right-Click Context Menus

#### Positions Table Context Menu
- **New Position**: Create new trading position
- **Modify Position**: Edit selected position
- **Close Position**: Close selected position
- **Copy Details**: Copy position information to clipboard
- **Export Data**: Export selected positions

#### Orders Table Context Menu
- **New Order**: Create new pending order
- **Modify Order**: Edit selected order
- **Cancel Order**: Cancel selected order
- **Copy Order ID**: Copy order reference
- **View Order History**: Show order modification history

#### Price Quotes Context Menu
- **New Position**: Quick position creation for symbol
- **Enable/Disable Trading**: Toggle symbol trading status
- **Symbol Information**: View detailed symbol specifications
- **Price History**: Access historical price data

### Table Navigation and Sorting

#### Sortable Table Headers
Click any column header to sort data:
- **First Click**: Sort ascending
- **Second Click**: Sort descending
- **Third Click**: Remove sorting

#### Advanced Sorting Features
- **Multi-Column Sort**: Hold Ctrl while clicking headers
- **Financial Data Sort**: Intelligent numeric sorting for prices and P/L
- **Date/Time Sort**: Chronological sorting with timezone awareness
- **Status Sort**: Logical ordering by status priority

#### Table Selection
- **Single Click**: Select individual row
- **Ctrl+Click**: Add/remove rows from selection
- **Shift+Click**: Select range of rows
- **Ctrl+A**: Select all visible rows

### Efficiency Tips

#### Quick Position Creation
1. Double-click any symbol in quotes panel
2. Modal opens with symbol pre-selected
3. Enter volume and click create
4. Use Tab to navigate fields quickly

#### Rapid Position Monitoring
1. Sort positions by P/L for performance overview
2. Use color coding for quick profit/loss identification
3. Monitor real-time updates without page refresh
4. Set up screen layout for optimal data visibility

#### Effective Filtering
1. Use date ranges for focused historical analysis
2. Combine symbol and account filters for precision
3. Save common filter combinations
4. Reset filters quickly with clear button

#### Multi-Tasking Features
- **Multiple Modal Support**: Open multiple edit modals simultaneously
- **Background Updates**: Data refreshes while working in modals
- **Session Persistence**: Maintain settings across login sessions
- **Quick Access**: Bookmark frequently used functions

---

## Security and Compliance

### Authentication Best Practices

#### Password Security
- **Strong Passwords**: Use complex passwords with mixed characters
- **Regular Updates**: Change passwords according to policy
- **Unique Credentials**: Don't reuse passwords from other systems
- **Secure Storage**: Never share or store passwords insecurely

#### Session Management
- **Automatic Timeout**: Sessions expire after inactivity period
- **Manual Logout**: Always log out when finished
- **Concurrent Sessions**: Monitor for unauthorized simultaneous logins
- **Session Monitoring**: Review login history regularly

#### CAPTCHA Integration
When enabled, CAPTCHA provides additional security:
- **Bot Prevention**: Prevents automated attacks
- **Brute Force Protection**: Slows down password guessing attempts
- **Enhanced Security**: Additional layer beyond username/password
- **Compliance**: Meets security requirements for financial platforms

### Audit Trail Interpretation

#### Understanding Audit Entries
Each audit log entry contains:
- **User**: Who performed the action
- **Action**: What operation was performed
- **Target**: What data was affected
- **Timestamp**: When the action occurred
- **Details**: Specific changes made
- **Source**: System component that logged the action

#### Critical Audit Events
Monitor these high-priority events:
1. **Authentication Failures**: Multiple failed login attempts
2. **Unauthorized Access**: Access outside normal hours
3. **Large Transactions**: Positions exceeding normal size
4. **Data Modifications**: Changes to historical records
5. **System Changes**: Configuration or permission updates

#### Compliance Reporting
Use audit logs for:
- **Regulatory Reports**: Required compliance documentation
- **Internal Reviews**: Management oversight reporting
- **Investigation Support**: Incident analysis and resolution
- **Performance Analysis**: User activity patterns and efficiency

### Risk Management Features

#### Position-Level Risk Controls
- **Stop Loss Orders**: Automatic loss limitation
- **Take Profit Orders**: Automatic profit realization
- **Position Sizing**: Volume limits per symbol
- **Margin Requirements**: Minimum equity maintenance

#### Account-Level Risk Controls
- **Margin Call Warnings**: Early risk alerts
- **Stop Out Protection**: Automatic position closure
- **Daily Loss Limits**: Maximum daily loss thresholds
- **Exposure Limits**: Maximum open position values

#### System-Level Risk Controls
- **Trading Halts**: Emergency trading suspension
- **Symbol Restrictions**: Individual symbol trading controls
- **Connection Monitoring**: Real-time system health checks
- **Data Validation**: Input verification and error prevention

#### Risk Monitoring Dashboard
The Dealer Analytics Panel provides:
- **Real-Time Metrics**: Live risk exposure updates
- **Alert System**: Visual and audio risk warnings
- **Trend Analysis**: Historical risk pattern tracking
- **Quick Actions**: Immediate risk mitigation tools

### Compliance Documentation

#### Record Keeping Requirements
Maintain complete records of:
- **Trading Activity**: All positions and orders
- **User Actions**: Complete audit trail
- **System Events**: Technical operations log
- **Communications**: Any trade-related discussions

#### Data Retention Policies
- **Minimum Retention**: Follow regulatory requirements
- **Secure Storage**: Protect data integrity and confidentiality
- **Access Controls**: Limit data access to authorized personnel
- **Backup Procedures**: Ensure data availability and recovery

---

## Troubleshooting

### Common Issues and Solutions

#### Login Problems

**Issue**: Cannot log in with correct credentials
**Solutions**:
1. Verify CAPTCHA completion if enabled
2. Check caps lock and keyboard layout
3. Clear browser cache and cookies
4. Try different browser or incognito mode
5. Contact system administrator for account status

**Issue**: CAPTCHA not loading or responding
**Solutions**:
1. Refresh the page and try again
2. Check internet connection stability
3. Disable browser extensions temporarily
4. Allow JavaScript execution for the site
5. Try different browser if problem persists

#### WebSocket Connection Problems

**Issue**: Red connection indicator showing disconnected
**Solutions**:
1. Check internet connection stability
2. Refresh browser page (F5)
3. Clear browser cache
4. Disable firewall/antivirus temporarily
5. Contact IT support for network configuration

**Issue**: Data not updating in real-time
**Solutions**:
1. Verify green connection indicator
2. Check if page is active (not minimized)
3. Refresh page to reset connection
4. Close other browser tabs to free resources
5. Restart browser if problem persists

#### Trading Operation Issues

**Issue**: Cannot create new positions
**Solutions**:
1. Check if trading is enabled for symbol
2. Verify sufficient account margin
3. Confirm account is not in stop-out status
4. Check for any trading restrictions
5. Validate position parameters

**Issue**: Orders not executing
**Solutions**:
1. Check order price relative to current market
2. Verify order hasn't expired
3. Confirm sufficient account funds
4. Check for symbol trading restrictions
5. Review order type and parameters

#### Data Display Problems

**Issue**: Tables not loading or showing empty
**Solutions**:
1. Refresh page data (F5)
2. Check filter settings and clear if needed
3. Verify date ranges are appropriate
4. Clear browser cache
5. Try different browser

**Issue**: Incorrect price formatting or calculations
**Solutions**:
1. Verify symbol specifications
2. Check decimal place settings
3. Refresh market data
4. Clear cache and reload
5. Report calculation errors to support

### Performance Optimization Tips

#### Browser Optimization
1. **Use Supported Browsers**: Chrome, Firefox, Safari, Edge latest versions
2. **Close Unused Tabs**: Free memory resources
3. **Regular Cache Clearing**: Prevent data conflicts
4. **Disable Unnecessary Extensions**: Reduce browser overhead
5. **Update Browser**: Keep current with latest version

#### Network Optimization
1. **Stable Connection**: Use wired connection when possible
2. **Sufficient Bandwidth**: Ensure adequate internet speed
3. **Firewall Configuration**: Allow WebSocket connections
4. **VPN Considerations**: May affect real-time data performance
5. **Network Monitoring**: Check for intermittent connectivity issues

#### System Performance
1. **Sufficient RAM**: Minimum 8GB recommended
2. **CPU Performance**: Modern processor for smooth operation
3. **Screen Resolution**: 1920x1080 minimum for full interface
4. **Multiple Monitors**: Enhance trading workspace efficiency
5. **Background Applications**: Close unnecessary programs

#### Data Management
1. **Regular Data Cleanup**: Archive old historical data
2. **Filter Usage**: Use appropriate filters to limit data loads
3. **Pagination**: Work with manageable data sets
4. **Export Functions**: Remove data from system when possible
5. **Cache Management**: Clear application cache regularly

### Error Messages and Solutions

#### Authentication Errors
- **"Invalid credentials"**: Check username/password accuracy
- **"Account locked"**: Contact administrator for unlock
- **"CAPTCHA failed"**: Retry CAPTCHA verification
- **"Session expired"**: Log out and log back in

#### Trading Errors
- **"Insufficient margin"**: Reduce position size or add funds
- **"Trading disabled"**: Check symbol trading status
- **"Invalid price"**: Verify price is within valid range
- **"Order rejected"**: Check order parameters and market conditions

#### Connection Errors
- **"WebSocket disconnected"**: Refresh page or check network
- **"Connection timeout"**: Verify internet connectivity
- **"Server unavailable"**: Wait and retry, contact support if persistent
- **"Authentication failed"**: Re-login to refresh session

#### Data Errors
- **"No data available"**: Check date ranges and filters
- **"Load failed"**: Refresh page and retry
- **"Permission denied"**: Verify user access rights
- **"Data corruption"**: Clear cache and reload

### Getting Support

#### Internal Support
1. **System Administrator**: Technical issues and account problems
2. **Trading Desk**: Trading operation questions
3. **Compliance Department**: Regulatory and audit questions
4. **IT Department**: Network and system configuration

#### Self-Help Resources
1. **User Manual**: This comprehensive guide
2. **System Status Page**: Real-time system health information
3. **FAQ Database**: Common questions and answers
4. **Video Tutorials**: Step-by-step operation guides

#### Emergency Procedures
1. **Trading Halt**: Contact trading desk immediately
2. **System Outage**: Use backup communication methods
3. **Security Breach**: Report to security team immediately
4. **Data Loss**: Contact IT support with details

---

## Appendices

### A. System Requirements

#### Minimum Requirements
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **RAM**: 8GB minimum, 16GB recommended
- **Screen Resolution**: 1920x1080 minimum
- **Internet**: Broadband connection (10Mbps minimum)
- **Operating System**: Windows 10+, macOS 10.15+, Linux (modern distributions)

#### Recommended Setup
- **Multiple Monitors**: 2-4 monitors for optimal workspace
- **High-Speed Internet**: 50Mbps+ for best performance
- **Dedicated Machine**: Separate computer for trading only
- **Backup Connection**: Secondary internet connection
- **UPS Power**: Uninterruptible power supply for critical operations

### B. Symbol Specifications

#### Forex Pairs
- **Major Pairs**: EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, USD/CAD, NZD/USD
- **Precision**: 5 decimal places (3 for JPY pairs)
- **Minimum Volume**: 0.01 lots
- **Maximum Volume**: 100 lots per position

#### Commodities
- **Gold (XAU/USD)**: 2 decimal places, 0.01 lot minimum
- **Silver (XAG/USD)**: 3 decimal places, 0.1 lot minimum
- **Oil (WTI/Brent)**: 2 decimal places, 0.01 lot minimum

#### Cryptocurrencies
- **Bitcoin (BTC/USD)**: 1 decimal place, 0.001 lot minimum
- **Ethereum (ETH/USD)**: 1 decimal place, 0.01 lot minimum
- **Other Cryptos**: Variable precision based on instrument

### C. Regulatory Information

#### Compliance Standards
- **MiFID II**: European regulatory compliance
- **CFTC**: US Commodity Futures Trading Commission
- **FCA**: UK Financial Conduct Authority
- **ASIC**: Australian Securities and Investments Commission

#### Audit Requirements
- **Trade Reporting**: Real-time transaction reporting
- **Record Keeping**: Minimum 7-year retention
- **Client Classification**: Professional vs Retail client handling
- **Best Execution**: Order execution quality monitoring

### D. Contact Information

#### Support Contacts
- **Technical Support**: <EMAIL>
- **Trading Desk**: <EMAIL>
- **Compliance**: <EMAIL>
- **Emergency**: <EMAIL>

#### Business Hours
- **Trading Support**: 24/5 (Sunday 17:00 EST - Friday 17:00 EST)
- **Technical Support**: 24/7
- **Compliance**: Monday-Friday 9:00-17:00 EST
- **Emergency**: 24/7

---

*This manual is current as of the latest system version. For updates and additional resources, please contact the support team or visit the internal documentation portal.*

**Document Version**: 1.0  
**Last Updated**: Current Date  
**Next Review**: Quarterly Update Schedule