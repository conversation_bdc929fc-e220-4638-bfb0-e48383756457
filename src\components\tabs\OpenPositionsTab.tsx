import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { Plus, Edit, X, Copy, Eye, BarChart3 } from 'lucide-react';
import NewPositionModal from '../modals/NewPositionModal';
import ModifyPositionModal from '../modals/ModifyPositionModal';
import ClosePositionModal from '../modals/ClosePositionModal';
import { formatPrice, formatPriceOrFallback } from '../../utils/priceFormatting';
import { useTableSort } from '../../hooks/useTableSort';
import SortableTableHeader from '../ui/SortableTableHeader';

const OpenPositionsTab = () => {
  const { positions, traderAccounts } = useWebSocket();
  const [showNewPosition, setShowNewPosition] = useState(false);
  const [modifyingPosition, setModifyingPosition] = useState<any>(null);
  const [closingPosition, setClosingPosition] = useState<any>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    position: any;
  } | null>(null);
  const [filters, setFilters] = useState({
    userGroup: 'all',
    accountId: '',
    symbol: ''
  });

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+N for new position
      if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        setShowNewPosition(true);
      }
      // Escape to close context menu
      if (event.key === 'Escape') {
        setContextMenu(null);
      }
    };

    const handleClickOutside = () => {
      setContextMenu(null);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Right-click context menu handler
  const handleContextMenu = useCallback((event: React.MouseEvent, position: any) => {
    event.preventDefault();
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      position
    });
  }, []);

  // Double-click handler for quick modify
  const handleDoubleClick = useCallback((position: any) => {
    setModifyingPosition(position);
  }, []);

  // Context menu actions
  const handleContextMenuAction = (action: string, position: any) => {
    setContextMenu(null);
    
    switch (action) {
      case 'modify':
        setModifyingPosition(position);
        break;
      case 'close':
        setClosingPosition(position);
        break;
      case 'copy':
        navigator.clipboard.writeText(`${position.symbol} ${position.type} ${position.volume} lots at ${position.openPrice}`);
        break;
      case 'details':
        // Could open a detailed view modal
        console.log('Show details for position:', position.positionId);
        break;
    }
  };

  // Filter positions based on current filters
  const filteredPositions = useMemo(() => {
    return positions.filter(position => {
      // User Group filter - look up the actual user group from traderAccounts
      if (filters.userGroup !== 'all') {
        const account = traderAccounts.find(acc => acc.accountId === position.accountId);
        if (account) {
          if (filters.userGroup === 'premium' && account.userGroup !== 'Premium Clients') return false;
          if (filters.userGroup === 'standard' && account.userGroup !== 'Standard Clients') return false;
        }
      }

      // Account ID filter
      if (filters.accountId && !position.accountId.toLowerCase().includes(filters.accountId.toLowerCase())) {
        return false;
      }

      // Symbol filter
      if (filters.symbol && !position.symbol.toLowerCase().includes(filters.symbol.toLowerCase())) {
        return false;
      }

      return true;
    });
  }, [positions, filters, traderAccounts]);

  // Add table sorting functionality
  const { sortedData: sortedPositions, handleSort, getSortDirection } = useTableSort(filteredPositions, 'pl');

  return (
    <div className="h-full flex flex-col">
      {/* Header with Filters and New Position Button */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3 flex-shrink-0 mb-4">
        {/* Title and New Position Button */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Open Positions</h3>
        <button
          onClick={() => setShowNewPosition(true)}
            className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors lg:ml-4"
            title="New Position (Ctrl+N)"
        >
          <Plus className="w-4 h-4" />
          <span className="text-sm">New Position</span>
        </button>
      </div>

        {/* Inline Filters */}
        <div className="flex flex-wrap gap-2 lg:gap-3">
          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">User Group</label>
          <select
            value={filters.userGroup}
            onChange={(e) => setFilters({ ...filters, userGroup: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="all">All Groups</option>
            <option value="premium">Premium Clients</option>
            <option value="standard">Standard Clients</option>
          </select>
        </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-40">
            <label className="block text-xs font-medium text-gray-400 mb-1">Account ID</label>
          <input
            type="text"
            placeholder="Search account..."
            value={filters.accountId}
            onChange={(e) => setFilters({ ...filters, accountId: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </div>

          <div className="min-w-0 flex-1 lg:flex-none lg:w-32">
            <label className="block text-xs font-medium text-gray-400 mb-1">Symbol</label>
          <input
            type="text"
            placeholder="e.g. EURUSD"
            value={filters.symbol}
            onChange={(e) => setFilters({ ...filters, symbol: e.target.value })}
              className="w-full px-2 py-1.5 bg-slate-600 border border-slate-500 rounded text-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
          </div>
        </div>
      </div>

      {/* Positions Table - Fill remaining height */}
      <div className="flex-1 overflow-y-auto border border-slate-600 rounded-lg">
        <table className="w-full">
          <thead className="sticky top-0 bg-slate-800 z-10">
            <tr className="border-b border-slate-600">
              <SortableTableHeader sortKey="accountId" onSort={handleSort} sortDirection={getSortDirection('accountId')}>
                Account ID
              </SortableTableHeader>
              <SortableTableHeader sortKey="positionId" onSort={handleSort} sortDirection={getSortDirection('positionId')}>
                Position ID
              </SortableTableHeader>
              <SortableTableHeader sortKey="symbol" onSort={handleSort} sortDirection={getSortDirection('symbol')}>
                Symbol
              </SortableTableHeader>
              <SortableTableHeader sortKey="type" onSort={handleSort} sortDirection={getSortDirection('type')}>
                Type
              </SortableTableHeader>
              <SortableTableHeader sortKey="volume" onSort={handleSort} sortDirection={getSortDirection('volume')}>
                Volume
              </SortableTableHeader>
              <SortableTableHeader sortKey="openPrice" onSort={handleSort} sortDirection={getSortDirection('openPrice')}>
                Open Price
              </SortableTableHeader>
              <SortableTableHeader sortKey="sl" onSort={handleSort} sortDirection={getSortDirection('sl')}>
                S/L
              </SortableTableHeader>
              <SortableTableHeader sortKey="tp" onSort={handleSort} sortDirection={getSortDirection('tp')}>
                T/P
              </SortableTableHeader>
              <SortableTableHeader sortKey="currentPrice" onSort={handleSort} sortDirection={getSortDirection('currentPrice')}>
                Current Price
              </SortableTableHeader>
              <SortableTableHeader sortKey="pl" onSort={handleSort} sortDirection={getSortDirection('pl')}>
                P/L
              </SortableTableHeader>
              <SortableTableHeader sortKey="openTime" onSort={handleSort} sortDirection={getSortDirection('openTime')}>
                Open Time
              </SortableTableHeader>
              <th className="text-left p-2 sm:p-3 text-xs sm:text-sm font-medium text-gray-400">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedPositions.length === 0 ? (
              <tr>
                <td colSpan={12} className="text-center py-8 text-gray-400">
                  No records found.
                </td>
              </tr>
            ) : (
              sortedPositions.map((position) => (
                <tr 
                  key={position.positionId} 
                  className="border-b border-slate-700 hover:bg-slate-700/50 cursor-pointer select-none"
                  onContextMenu={(e) => handleContextMenu(e, position)}
                  onDoubleClick={() => handleDoubleClick(position)}
                  title="Right-click for options, double-click to modify"
                >
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{position.accountId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{position.positionId}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-medium">{position.symbol}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm">
                    <span className={`px-2 py-1 rounded text-xs ${
                      position.type === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}>
                      {position.type}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white">{position.volume}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(position.openPrice, position.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPriceOrFallback(position.sl, position.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPriceOrFallback(position.tp, position.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-white font-mono">{formatPrice(position.currentPrice, position.symbol)}</td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm font-mono">
                    <span className={position.pl >= 0 ? 'text-green-400' : 'text-red-400'}>
                      {position.pl >= 0 ? '+' : ''}${position.pl.toFixed(2)}
                    </span>
                  </td>
                  <td className="p-2 sm:p-3 text-xs sm:text-sm text-gray-400">{position.openTime}</td>
                  <td className="p-2 sm:p-3">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setModifyingPosition(position);
                        }}
                        className="p-1 text-blue-400 hover:text-blue-300 transition-colors"
                        title="Modify (or double-click row)"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setClosingPosition(position);
                        }}
                        className="p-1 text-red-400 hover:text-red-300 transition-colors"
                        title="Close (or right-click for menu)"
                      >
                        <X className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Modals */}
      {showNewPosition && (
        <NewPositionModal
          isOpen={showNewPosition}
          onClose={() => setShowNewPosition(false)}
        />
      )}
      
      {modifyingPosition && (
        <ModifyPositionModal
          isOpen={!!modifyingPosition}
          position={modifyingPosition}
          onClose={() => setModifyingPosition(null)}
        />
      )}
      
      {closingPosition && (
        <ClosePositionModal
          isOpen={!!closingPosition}  
          position={closingPosition}
          onClose={() => setClosingPosition(null)}
        />
      )}

      {/* Context Menu */}
      {contextMenu && (
        <div
          className="fixed bg-slate-800 border border-slate-600 rounded-lg shadow-xl z-50 py-1 min-w-[160px]"
          style={{
            left: `${contextMenu.x}px`,
            top: `${contextMenu.y}px`,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <button
            onClick={() => handleContextMenuAction('modify', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <Edit className="w-4 h-4" />
            <span>Modify Position</span>
          </button>
          <button
            onClick={() => handleContextMenuAction('close', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <X className="w-4 h-4" />
            <span>Close Position</span>
          </button>
          <div className="border-t border-slate-600 my-1"></div>
          <button
            onClick={() => handleContextMenuAction('copy', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <Copy className="w-4 h-4" />
            <span>Copy Details</span>
          </button>
          <button
            onClick={() => handleContextMenuAction('details', contextMenu.position)}
            className="w-full px-3 py-2 text-left text-sm text-white hover:bg-slate-700 flex items-center space-x-2"
          >
            <Eye className="w-4 h-4" />
            <span>View Details</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default OpenPositionsTab;
