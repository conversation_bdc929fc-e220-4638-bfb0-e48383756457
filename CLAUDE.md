# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Essential Commands
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Build for development mode
npm run build:dev

# Lint code
npm run lint

# Preview production build
npm run preview
```

### Development Server
- Runs on `http://localhost:8080` (configured in vite.config.ts)
- Uses Vite with React SWC for fast development
- Hot module replacement enabled

## Architecture Overview

### Tech Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Components**: shadcn/ui built on Radix UI
- **Styling**: Tailwind CSS with custom design system
- **State Management**: React Context API
- **Forms**: React Hook Form with Zod validation
- **Data Fetching**: TanStack Query (React Query)
- **Charts**: Recharts
- **Icons**: Lucide React

### Application Structure
This is a financial trading terminal application with the following key architecture:

#### Context Providers
- **AuthContext** (`src/contexts/AuthContext.tsx`): Handles user authentication with mock login system
  - Demo credentials: `dealer01` / `password123`
  - Supports CAPTCHA integration (see CAPTCHA_IMPLEMENTATION_GUIDE.md)
- **WebSocketContext** (`src/contexts/WebSocketContext.tsx`): Manages all trading data and real-time updates
  - Mock WebSocket connection with simulated real-time price feeds
  - Manages positions, orders, quotes, deal history, and audit logs
  - Handles CRUD operations for trading entities

#### Main Components
- **Dashboard** (`src/components/Dashboard.tsx`): Main trading interface
- **LoginPage** (`src/components/LoginPage.tsx`): Authentication interface
- **Trading Panels**: Price quotes, positions, orders, trader accounts
- **Modal System**: Comprehensive modals for creating/editing positions and orders
- **History Tabs**: Position history, order history, deal history, audit logs

#### Data Models
The application handles complex financial data structures:
- **Quotes**: Real-time price feeds with bid/ask/spread
- **Positions**: Open trading positions with P/L calculations
- **Orders**: Pending orders (limit, stop orders)
- **Deals**: Transaction history with detailed execution data
- **Trader Accounts**: Account balances, equity, margin levels
- **Audit Log**: Comprehensive activity tracking with change history

### Component Organization
```
src/
├── components/
│   ├── modals/           # Trading operation modals
│   ├── tabs/             # History and data display tabs
│   └── ui/               # shadcn/ui components
├── contexts/             # React contexts for state management
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
├── pages/                # Page components
└── utils/                # Helper utilities
```

## Configuration

### TypeScript
- Uses composite TypeScript configuration
- Path aliases: `@/*` maps to `./src/*`
- Relaxed TypeScript settings for rapid development

### Tailwind CSS
- Custom design system with CSS variables
- Dark mode support with `class` strategy
- Extended color palette for financial UI
- Custom animations and utilities

### ESLint
- TypeScript ESLint configuration
- React hooks and refresh plugins
- Unused variables warnings disabled for development

## Mock Data System

The application uses extensive mock data to simulate a real trading environment:
- **Real-time price updates**: Simulated market data with realistic volatility
- **Account states**: Including margin call and stop-out scenarios
- **Historical data**: Pre-populated trading history
- **Audit trail**: Comprehensive logging of all user actions

## Development Guidelines

### Trading Operations
When working with trading functionality:
- All operations create audit log entries automatically
- Position/order modifications track field-level changes
- Price calculations use proper financial formatting
- Real-time updates maintain data consistency

### State Management
- Use WebSocketContext for all trading data
- AuthContext for authentication state
- Prefer context over prop drilling for shared state

### UI Components
- Use shadcn/ui components from `src/components/ui/`
- Follow established color scheme and spacing
- Maintain responsive design patterns

### Security Features
- CAPTCHA integration ready (see env-setup-guide.md)
- Rate limiting architecture in place
- Audit logging for compliance

## Testing

No test framework is currently configured. When adding tests:
- Consider using Vitest (matches Vite ecosystem)
- Focus on trading calculation logic
- Test context providers and data transformations

## Deployment

- Built for Lovable platform deployment
- Vite optimized builds
- Environment variable support for CAPTCHA keys
- Production build includes component tagging removal

## Important Notes

- Demo application with realistic trading interface
- All financial data is simulated
- CAPTCHA implementation available but optional
- Comprehensive audit logging built-in
- Real-time updates using intervals (not actual WebSocket)