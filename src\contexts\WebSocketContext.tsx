import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { formatPrice } from '../utils/priceFormatting';

interface Quote {
  symbol: string;
  bid: number;
  ask: number;
  spread: number;
  dailyChange: number;
  time: string;
}

interface Position {
  accountId: string;
  positionId: string;
  symbol: string;
  type: string;
  volume: number;
  openPrice: number;
  sl?: number;
  tp?: number;
  currentPrice: string;
  swap: number;
  commission: number;
  pl: number;
  openTime: string;
}

interface Order {
  accountId: string;
  orderId: string;
  symbol: string;
  type: string;
  volume: number;
  orderPrice: number;
  sl?: number;
  tp?: number;
  currentPrice: string;
  state: string;
  placementTime: string;
  expiration?: string;
}

interface Deal {
  executionTime: string;
  dealId: string;
  accountId: string;
  symbol?: string;
  type: 'balance' | 'buy' | 'sell';
  direction?: 'in' | 'out';
  volume?: number;
  executionPrice?: number;
  orderId?: string;
  commission: number;
  swap: number;
  profitLoss: number;
  balance: number;
  comment?: string;
}

interface TraderAccount {
  accountId: string;
  userGroup: string;
  balance: number;
  creditLimit: number;
  floatingPnL: number;
  equity: number;
  currency: string;
  usedMargin: number;
  marginLevel: number;
  marginCall: number;
  stopOut: number;
}

interface AuditEntry {
  id: string;
  timestamp: string;
  userId: string;
  action: 'CREATE' | 'EDIT' | 'DELETE' | 'CLOSE' | 'CANCEL' | 'LOGIN' | 'LOGOUT';
  entityType: 'Position' | 'Order' | 'Deal' | 'Account' | 'System';
  entityId: string;
  details: string;
  ipAddress: string;
  sessionId: string;
  accountId?: string;
  symbol?: string;
  changes?: { field: string; from: any; to: any }[];
}

interface WebSocketContextType {
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  quotes: Quote[];
  positions: Position[];
  orders: Order[];
  positionHistory: any[];
  orderHistory: any[];
  dealHistory: Deal[];
  traderAccounts: TraderAccount[];
  auditLog: AuditEntry[];
  tradingDisabledSymbols: Set<string>;
  allTradingDisabled: boolean;
  updatePosition: (positionId: string, updates: { sl?: number; tp?: number }) => void;
  updateOrder: (orderId: string, updates: { sl?: number; tp?: number }) => void;
  addPosition: (position: Omit<Position, 'positionId' | 'currentPrice' | 'pl'>) => void;
  addOrder: (order: Omit<Order, 'orderId' | 'currentPrice' | 'state'>) => void;
  closePosition: (positionId: string, volume: number) => void;
  cancelOrder: (orderId: string) => void;
  deletePositionHistory: (positionId: string) => Promise<void>;
  deleteOrderHistory: (orderId: string) => Promise<void>;
  deleteDealHistory: (dealId: string) => Promise<void>;
  addAuditEntry: (entry: Omit<AuditEntry, 'id' | 'timestamp' | 'userId' | 'ipAddress' | 'sessionId'>) => void;
  toggleSymbolTradingDisabled: (symbol: string) => void;
  toggleAllTradingDisabled: () => void;
  updateTradingDisabledSymbols: (symbols: Set<string>) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export const WebSocketProvider = ({ children }: { children: ReactNode }) => {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connecting');
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [positionHistory, setPositionHistory] = useState<any[]>([]);
  const [orderHistory, setOrderHistory] = useState<any[]>([]);
  const [dealHistory, setDealHistory] = useState<Deal[]>([]);
  const [traderAccounts, setTraderAccounts] = useState<TraderAccount[]>([]);
  const [auditLog, setAuditLog] = useState<AuditEntry[]>([]);
  const [tradingDisabledSymbols, setTradingDisabledSymbols] = useState<Set<string>>(new Set());
  const [allTradingDisabled, setAllTradingDisabled] = useState(false);

  // Current user info (in real app this would come from auth context)
  const currentUser = {
    userId: 'DEALER001',
    ipAddress: '*************',
    sessionId: 'SESSION_' + Date.now()
  };

  const addAuditEntry = (entry: Omit<AuditEntry, 'id' | 'timestamp' | 'userId' | 'ipAddress' | 'sessionId'>) => {
    const auditEntry: AuditEntry = {
      ...entry,
      id: 'AUDIT_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      userId: currentUser.userId,
      ipAddress: currentUser.ipAddress,
      sessionId: currentUser.sessionId
    };
    
    setAuditLog(prev => [auditEntry, ...prev]);
  };

  const updatePosition = (positionId: string, updates: { sl?: number; tp?: number }) => {
    setPositions(prevPositions => {
      const oldPosition = prevPositions.find(p => p.positionId === positionId);
      if (!oldPosition) return prevPositions;

      const changes: { field: string; from: any; to: any }[] = [];
      if (updates.sl !== undefined && updates.sl !== oldPosition.sl) {
        changes.push({ field: 'Stop Loss', from: oldPosition.sl || 'None', to: updates.sl });
      }
      if (updates.tp !== undefined && updates.tp !== oldPosition.tp) {
        changes.push({ field: 'Take Profit', from: oldPosition.tp || 'None', to: updates.tp });
      }

      if (changes.length > 0) {
        addAuditEntry({
          action: 'EDIT',
          entityType: 'Position',
          entityId: positionId,
          accountId: oldPosition.accountId,
          symbol: oldPosition.symbol,
          details: `Modified position ${positionId}: ${changes.map(c => `${c.field} changed from ${c.from} to ${c.to}`).join(', ')}`,
          changes
        });
      }

      return prevPositions.map(position =>
        position.positionId === positionId
          ? { ...position, ...updates }
          : position
    );
    });
  };

  const updateOrder = (orderId: string, updates: { sl?: number; tp?: number }) => {
    setOrders(prevOrders => {
      const oldOrder = prevOrders.find(o => o.orderId === orderId);
      if (!oldOrder) return prevOrders;

      const changes: { field: string; from: any; to: any }[] = [];
      if (updates.sl !== undefined && updates.sl !== oldOrder.sl) {
        changes.push({ field: 'Stop Loss', from: oldOrder.sl || 'None', to: updates.sl });
      }
      if (updates.tp !== undefined && updates.tp !== oldOrder.tp) {
        changes.push({ field: 'Take Profit', from: oldOrder.tp || 'None', to: updates.tp });
      }

      if (changes.length > 0) {
        addAuditEntry({
          action: 'EDIT',
          entityType: 'Order',
          entityId: orderId,
          accountId: oldOrder.accountId,
          symbol: oldOrder.symbol,
          details: `Modified order ${orderId}: ${changes.map(c => `${c.field} changed from ${c.from} to ${c.to}`).join(', ')}`,
          changes
        });
      }

      return prevOrders.map(order =>
        order.orderId === orderId
          ? { ...order, ...updates }
          : order
    );
    });
  };

  const closePosition = (positionId: string, volume: number) => {
    setPositions(prevPositions => {
      const positionToClose = prevPositions.find(p => p.positionId === positionId);
      if (!positionToClose) return prevPositions;

      // Add to history
      const historyRecord = {
        ...positionToClose,
        closeTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        closedVolume: volume,
        status: 'CLOSED',
        finalPL: positionToClose.pl
      };
      setPositionHistory(prev => [historyRecord, ...prev]);

      // Create deal record for position closing
      const closeDeal: Deal = {
        executionTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        dealId: `DEAL${Date.now().toString().slice(-6)}`,
        accountId: positionToClose.accountId,
        symbol: positionToClose.symbol,
        type: positionToClose.type === 'BUY' ? 'sell' : 'buy', // Opposite type for closing
        direction: 'out',
        volume: volume,
        executionPrice: parseFloat(positionToClose.currentPrice),
        orderId: positionToClose.positionId,
        commission: volume === positionToClose.volume ? positionToClose.commission : (positionToClose.commission * (volume / positionToClose.volume)),
        swap: volume === positionToClose.volume ? positionToClose.swap : (positionToClose.swap * (volume / positionToClose.volume)),
        profitLoss: volume === positionToClose.volume ? positionToClose.pl : (positionToClose.pl * (volume / positionToClose.volume)),
        balance: 10000.00 + positionToClose.pl // Mock balance calculation
      };

      setDealHistory(prevDeals => [closeDeal, ...prevDeals]);

      // Add audit entry
      addAuditEntry({
        action: 'CLOSE',
        entityType: 'Position',
        entityId: positionId,
        accountId: positionToClose.accountId,
        symbol: positionToClose.symbol,
        details: `Closed position ${positionId} - ${positionToClose.symbol} ${positionToClose.type} ${volume} lots at ${positionToClose.currentPrice}, P/L: ${positionToClose.pl >= 0 ? '+' : ''}$${positionToClose.pl.toFixed(2)}`
      });

      // If partial close, update volume; if full close, remove position
      if (volume >= positionToClose.volume) {
        return prevPositions.filter(p => p.positionId !== positionId);
      } else {
        return prevPositions.map(p =>
          p.positionId === positionId
            ? { ...p, volume: p.volume - volume }
            : p
        );
      }
    });
  };

  const cancelOrder = (orderId: string) => {
    setOrders(prevOrders => {
      const orderToCancel = prevOrders.find(o => o.orderId === orderId);
      if (!orderToCancel) return prevOrders;

      // Add to history
      const historyRecord = {
        ...orderToCancel,
        cancelTime: new Date().toLocaleString(),
        status: 'CANCELLED'
      };
      setOrderHistory(prev => [historyRecord, ...prev]);

      // Add audit entry
      addAuditEntry({
        action: 'CANCEL',
        entityType: 'Order',
        entityId: orderId,
        accountId: orderToCancel.accountId,
        symbol: orderToCancel.symbol,
        details: `Cancelled order ${orderId} - ${orderToCancel.symbol} ${orderToCancel.type} ${orderToCancel.volume} lots at ${orderToCancel.orderPrice}`
      });

      return prevOrders.filter(o => o.orderId !== orderId);
    });
  };

  const deletePositionHistory = async (positionId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setPositionHistory(prevHistory => {
        const recordToDelete = prevHistory.find(p => p.positionId === positionId);
        if (!recordToDelete) {
          reject(new Error('Position record not found'));
          return prevHistory;
        }

        // Add audit entry
        addAuditEntry({
          action: 'DELETE',
          entityType: 'Position',
          entityId: positionId,
          accountId: recordToDelete.accountId,
          symbol: recordToDelete.symbol,
          details: `DELETED position history record ${positionId} - ${recordToDelete.symbol} ${recordToDelete.type} ${recordToDelete.volume} lots, Final P/L: ${recordToDelete.finalPL >= 0 ? '+' : ''}$${recordToDelete.finalPL.toFixed(2)}`
        });

        resolve();
        return prevHistory.filter(p => p.positionId !== positionId);
      });
    });
  };

  const deleteOrderHistory = async (orderId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setOrderHistory(prevHistory => {
        const recordToDelete = prevHistory.find(o => o.orderId === orderId);
        if (!recordToDelete) {
          reject(new Error('Order record not found'));
          return prevHistory;
        }

        // Add audit entry
        addAuditEntry({
          action: 'DELETE',
          entityType: 'Order',
          entityId: orderId,
          accountId: recordToDelete.accountId,
          symbol: recordToDelete.symbol,
          details: `DELETED order history record ${orderId} - ${recordToDelete.symbol} ${recordToDelete.type} ${recordToDelete.volume} lots at ${recordToDelete.orderPrice}`
        });

        resolve();
        return prevHistory.filter(o => o.orderId !== orderId);
      });
    });
  };

  const deleteDealHistory = async (dealId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      setDealHistory(prevHistory => {
        const recordToDelete = prevHistory.find(d => d.dealId === dealId);
        if (!recordToDelete) {
          reject(new Error('Deal record not found'));
          return prevHistory;
        }

        // Add audit entry
        addAuditEntry({
          action: 'DELETE',
          entityType: 'Deal',
          entityId: dealId,
          accountId: recordToDelete.accountId,
          symbol: recordToDelete.symbol,
          details: `DELETED deal history record ${dealId} - ${recordToDelete.symbol} ${recordToDelete.type} ${recordToDelete.volume} lots at ${recordToDelete.executionPrice}`
        });

        resolve();
        return prevHistory.filter(d => d.dealId !== dealId);
      });
    });
  };

  const addPosition = (position: Omit<Position, 'positionId' | 'currentPrice' | 'pl'>) => {
    const newPosition: Position = {
      ...position,
      positionId: `POS${Date.now().toString().slice(-6)}`,
      currentPrice: '0.00000',
      pl: 0.00
    };

    setPositions(prevPositions => [newPosition, ...prevPositions]);

    // Create corresponding deal record
    const newDeal: Deal = {
      executionTime: position.openTime,
      dealId: `DEAL${Date.now().toString().slice(-6)}`,
      accountId: position.accountId,
      symbol: position.symbol,
      type: position.type.toLowerCase() as 'buy' | 'sell',
      direction: 'in',
      volume: position.volume,
      executionPrice: position.openPrice,
      orderId: newPosition.positionId,
      commission: position.commission,
      swap: position.swap,
      profitLoss: 0.00,
      balance: 10000.00 // Mock balance - in real app this would be calculated
    };

    setDealHistory(prevDeals => [newDeal, ...prevDeals]);

    // Add audit entry
    addAuditEntry({
      action: 'CREATE',
      entityType: 'Position',
      entityId: newPosition.positionId,
      accountId: newPosition.accountId,
      symbol: newPosition.symbol,
      details: `Created new position ${newPosition.positionId} - ${newPosition.symbol} ${newPosition.type} ${newPosition.volume} lots at ${newPosition.openPrice}${newPosition.sl ? `, SL: ${newPosition.sl}` : ''}${newPosition.tp ? `, TP: ${newPosition.tp}` : ''}`
    });
  };

  const addOrder = (order: Omit<Order, 'orderId' | 'currentPrice' | 'state'>) => {
    const newOrder: Order = {
      ...order,
      orderId: `ORD${Date.now().toString().slice(-6)}`,
      currentPrice: '0.00000',
      state: 'PENDING'
    };

    setOrders(prevOrders => [newOrder, ...prevOrders]);

    // Add audit entry
    addAuditEntry({
      action: 'CREATE',
      entityType: 'Order',
      entityId: newOrder.orderId,
      accountId: newOrder.accountId,
      symbol: newOrder.symbol,
      details: `Created new order ${newOrder.orderId} - ${newOrder.symbol} ${newOrder.type} ${newOrder.volume} lots at ${newOrder.orderPrice}${newOrder.sl ? `, SL: ${newOrder.sl}` : ''}${newOrder.tp ? `, TP: ${newOrder.tp}` : ''}`
    });
  };

  const toggleSymbolTradingDisabled = (symbol: string) => {
    setTradingDisabledSymbols(prev => {
      const newTradingDisabledSymbols = new Set(prev);
      if (prev.has(symbol)) {
        newTradingDisabledSymbols.delete(symbol);
      } else {
        newTradingDisabledSymbols.add(symbol);
      }
      return newTradingDisabledSymbols;
    });
  };

  const toggleAllTradingDisabled = () => {
    const wasDisabled = allTradingDisabled;
    setAllTradingDisabled(prev => !prev);
    
    // If enabling all trading, also clear individual symbol restrictions
    if (wasDisabled) {
      setTradingDisabledSymbols(new Set());
    }
  };

  const updateTradingDisabledSymbols = (symbols: Set<string>) => {
    setTradingDisabledSymbols(symbols);
  };

  useEffect(() => {
    // Simulate WebSocket connection
    const connectWebSocket = () => {
      setConnectionStatus('connecting');
      
      // Simulate connection delay
      setTimeout(() => {
        setConnectionStatus('connected');
        
        // Initialize mock data
        const mockQuotes: Quote[] = [
          { symbol: 'EURUSD', bid: 1.08547, ask: 1.08564, spread: 0.2, dailyChange: 0.0012, time: new Date().toLocaleTimeString() },
          { symbol: 'GBPUSD', bid: 1.26396, ask: 1.26339, spread: 0.3, dailyChange: -0.0008, time: new Date().toLocaleTimeString() },
          { symbol: 'USDJPY', bid: 149.82009, ask: 149.85070, spread: 0.3, dailyChange: 0.0045, time: new Date().toLocaleTimeString() },
          { symbol: 'AUDUSD', bid: 0.65369, ask: 0.65499, spread: 0.3, dailyChange: 0.0023, time: 'STALE_DATA' },
          { symbol: 'USDCAD', bid: 1.36811, ask: 1.36865, spread: 0.3, dailyChange: -0.0015, time: new Date().toLocaleTimeString() },
          { symbol: 'XAUUSD', bid: 2052.45, ask: 2053.25, spread: 0.8, dailyChange: 0.0145, time: new Date().toLocaleTimeString() },
          { symbol: 'XAGUSD', bid: 24.123, ask: 24.145, spread: 0.22, dailyChange: -0.0089, time: new Date().toLocaleTimeString() },
          { symbol: 'BTCUSD', bid: 43567.2, ask: 43589.8, spread: 22.6, dailyChange: 0.0234, time: new Date().toLocaleTimeString() },
          { symbol: 'ETHUSD', bid: 2547.85, ask: 2549.12, spread: 1.27, dailyChange: 0.0189, time: new Date().toLocaleTimeString() },
          { symbol: 'EURJPY', bid: 162.445, ask: 162.520, spread: 0.75, dailyChange: 0.0031, time: new Date().toLocaleTimeString() },
          { symbol: 'GBPJPY', bid: 189.234, ask: 189.315, spread: 0.81, dailyChange: -0.0022, time: new Date().toLocaleTimeString() },
        ];
        
        const mockPositions: Position[] = [
          {
            accountId: 'ACC001',
            positionId: 'POS001',
            symbol: 'EURUSD',
            type: 'BUY',
            volume: 1.0,
            openPrice: 1.0835,
            sl: 1.0800,
            tp: 1.0900,
            currentPrice: '1.0847',
            swap: -2.45,
            commission: -8.50,
            pl: 120.00,
            openTime: '2024-06-19 09:15:23'
          },
          {
            accountId: 'ACC002',
            positionId: 'POS002',
            symbol: 'GBPUSD',
            type: 'SELL',
            volume: 0.5,
            openPrice: 1.2650,
            sl: 1.2700,
            tp: 1.2600,
            currentPrice: '1.2634',
            swap: 1.25,
            commission: -4.25,
            pl: 80.00,
            openTime: '2024-06-19 10:30:15'
          },
          {
            accountId: 'ACC003',
            positionId: 'POS003',
            symbol: 'USDJPY',
            type: 'BUY',
            volume: 2.0,
            openPrice: 149.50,
            sl: 148.80,
            tp: 151.00,
            currentPrice: '149.82',
            swap: -1.20,
            commission: -12.00,
            pl: 640.00,
            openTime: '2024-06-19 11:45:10'
          },
          {
            accountId: 'ACC004',
            positionId: 'POS004',
            symbol: 'AUDUSD',
            type: 'SELL',
            volume: 1.5,
            openPrice: 0.6580,
            sl: 0.6620,
            tp: 0.6520,
            currentPrice: '0.6542',
            swap: 0.85,
            commission: -6.75,
            pl: 570.00,
            openTime: '2024-06-19 12:20:35'
          },
          {
            accountId: 'ACC005',
            positionId: 'POS005',
            symbol: 'USDCAD',
            type: 'BUY',
            volume: 0.8,
            openPrice: 1.3650,
            sl: 1.3600,
            tp: 1.3750,
            currentPrice: '1.3687',
            swap: -0.95,
            commission: -5.40,
            pl: 236.00,
            openTime: '2024-06-19 13:15:42'
          },
          {
            accountId: 'ACC001',
            positionId: 'POS006',
            symbol: 'EURUSD',
            type: 'SELL',
            volume: 0.5,
            openPrice: 1.0865,
            sl: 1.0900,
            tp: 1.0820,
            currentPrice: '1.0847',
            swap: 0.60,
            commission: -4.25,
            pl: 90.00,
            openTime: '2024-06-19 14:30:18'
          },
          {
            accountId: 'ACC006',
            positionId: 'POS007',
            symbol: 'GBPUSD',
            type: 'BUY',
            volume: 1.2,
            openPrice: 1.2590,
            sl: 1.2550,
            tp: 1.2680,
            currentPrice: '1.2634',
            swap: -1.40,
            commission: -8.10,
            pl: 528.00,
            openTime: '2024-06-19 15:45:25'
          },
          {
            accountId: 'ACC007',
            positionId: 'POS008',
            symbol: 'USDJPY',
            type: 'SELL',
            volume: 0.7,
            openPrice: 150.20,
            sl: 150.80,
            tp: 149.40,
            currentPrice: '149.82',
            swap: 0.75,
            commission: -5.25,
            pl: 266.00,
            openTime: '2024-06-19 16:10:55'
          },
          {
            accountId: 'ACC002',
            positionId: 'POS009',
            symbol: 'EURUSD',
            type: 'BUY',
            volume: 1.5,
            openPrice: 1.0825,
            sl: 1.0780,
            tp: 1.0880,
            currentPrice: '1.0847',
            swap: -2.10,
            commission: -10.50,
            pl: 330.00,
            openTime: '2024-06-19 17:20:12'
          },
          {
            accountId: 'ACC003',
            positionId: 'POS010',
            symbol: 'GBPUSD',
            type: 'SELL',
            volume: 0.8,
            openPrice: 1.2680,
            sl: 1.2720,
            tp: 1.2620,
            currentPrice: '1.2634',
            swap: 1.10,
            commission: -6.40,
            pl: 368.00,
            openTime: '2024-06-19 18:35:45'
          },
          {
            accountId: 'ACC004',
            positionId: 'POS011',
            symbol: 'USDJPY',
            type: 'BUY',
            volume: 1.0,
            openPrice: 149.20,
            sl: 148.50,
            tp: 150.50,
            currentPrice: '149.82',
            swap: -0.80,
            commission: -7.00,
            pl: 620.00,
            openTime: '2024-06-19 19:50:30'
          },
          {
            accountId: 'ACC005',
            positionId: 'POS012',
            symbol: 'AUDUSD',
            type: 'SELL',
            volume: 2.0,
            openPrice: 0.6590,
            sl: 0.6630,
            tp: 0.6530,
            currentPrice: '0.6542',
            swap: 1.50,
            commission: -14.00,
            pl: 960.00,
            openTime: '2024-06-19 20:15:18'
          },
          {
            accountId: 'ACC006',
            positionId: 'POS013',
            symbol: 'USDCAD',
            type: 'BUY',
            volume: 1.2,
            openPrice: 1.3620,
            sl: 1.3570,
            tp: 1.3720,
            currentPrice: '1.3687',
            swap: -1.80,
            commission: -8.40,
            pl: 804.00,
            openTime: '2024-06-19 21:40:22'
          },
          {
            accountId: 'ACC007',
            positionId: 'POS014',
            symbol: 'EURUSD',
            type: 'SELL',
            volume: 0.6,
            openPrice: 1.0880,
            sl: 1.0920,
            tp: 1.0830,
            currentPrice: '1.0847',
            swap: 0.70,
            commission: -5.10,
            pl: 198.00,
            openTime: '2024-06-19 22:25:55'
          },
          {
            accountId: 'ACC001',
            positionId: 'POS015',
            symbol: 'GBPUSD',
            type: 'BUY',
            volume: 2.5,
            openPrice: 1.2580,
            sl: 1.2530,
            tp: 1.2650,
            currentPrice: '1.2634',
            swap: -3.50,
            commission: -17.50,
            pl: 1350.00,
            openTime: '2024-06-20 08:10:15'
          },
          {
            accountId: 'ACC002',
            positionId: 'POS016',
            symbol: 'USDJPY',
            type: 'SELL',
            volume: 1.8,
            openPrice: 150.50,
            sl: 151.20,
            tp: 149.30,
            currentPrice: '149.82',
            swap: 1.80,
            commission: -12.60,
            pl: 1224.00,
            openTime: '2024-06-20 09:35:40'
          },
          {
            accountId: 'ACC003',
            positionId: 'POS017',
            symbol: 'AUDUSD',
            type: 'BUY',
            volume: 1.3,
            openPrice: 0.6520,
            sl: 0.6480,
            tp: 0.6580,
            currentPrice: '0.6542',
            swap: -1.30,
            commission: -9.10,
            pl: 286.00,
            openTime: '2024-06-20 10:55:28'
          },
          {
            accountId: 'ACC004',
            positionId: 'POS018',
            symbol: 'USDCAD',
            type: 'SELL',
            volume: 0.9,
            openPrice: 1.3720,
            sl: 1.3770,
            tp: 1.3650,
            currentPrice: '1.3687',
            swap: 1.20,
            commission: -6.30,
            pl: 297.00,
            openTime: '2024-06-20 11:20:50'
          },
          {
            accountId: 'ACC005',
            positionId: 'POS019',
            symbol: 'EURUSD',
            type: 'BUY',
            volume: 3.0,
            openPrice: 1.0800,
            sl: 1.0750,
            tp: 1.0870,
            currentPrice: '1.0847',
            swap: -4.20,
            commission: -21.00,
            pl: 1410.00,
            openTime: '2024-06-20 12:45:33'
          },
          {
            accountId: 'ACC006',
            positionId: 'POS020',
            symbol: 'GBPUSD',
            type: 'SELL',
            volume: 1.1,
            openPrice: 1.2700,
            sl: 1.2750,
            tp: 1.2620,
            currentPrice: '1.2634',
            swap: 1.40,
            commission: -7.70,
            pl: 726.00,
            openTime: '2024-06-20 14:15:42'
          }
        ];
        
        const mockOrders: Order[] = [
          {
            accountId: 'ACC001',
            orderId: 'ORD001',
            symbol: 'EURUSD',
            type: 'BUY LIMIT',
            volume: 0.5,
            orderPrice: 1.0800,
            sl: 1.0750,
            tp: 1.0850,
            currentPrice: '1.0847',
            state: 'PENDING',
            placementTime: '2024-06-19 11:00:00',
            expiration: '2024-06-20 11:00:00'
          },
          {
            accountId: 'ACC003',
            orderId: 'ORD003',
            symbol: 'USDJPY',
            type: 'BUY STOP',
            volume: 1.2,
            orderPrice: 150.00,
            sl: 149.50,
            tp: 150.75,
            currentPrice: '149.820',
            state: 'PENDING',
            placementTime: '2024-06-19 09:45:00'
          },
          {
            accountId: 'ACC004',
            orderId: 'ORD004',
            symbol: 'XAUUSD',
            type: 'SELL LIMIT',
            volume: 0.3,
            orderPrice: 2060.00,
            sl: 2070.00,
            tp: 2040.00,
            currentPrice: '2052.45',
            state: 'PENDING',
            placementTime: '2024-06-19 08:15:00',
            expiration: '2024-06-22 08:15:00'
          },
          {
            accountId: 'ACC005',
            orderId: 'ORD005',
            symbol: 'AUDUSD',
            type: 'BUY LIMIT',
            volume: 1.5,
            orderPrice: 0.6500,
            sl: 0.6450,
            tp: 0.6580,
            currentPrice: '0.6537',
            state: 'PENDING',
            placementTime: '2024-06-19 07:20:00',
            expiration: '2024-06-20 07:20:00'
          },
          {
            accountId: 'ACC006',
            orderId: 'ORD006',
            symbol: 'EURJPY',
            type: 'SELL STOP',
            volume: 0.7,
            orderPrice: 161.50,
            sl: 162.00,
            tp: 160.80,
            currentPrice: '162.445',
            state: 'PENDING',
            placementTime: '2024-06-18 16:40:00',
            expiration: '2024-06-25 16:40:00'
          }
        ];
        
        const mockTraderAccounts: TraderAccount[] = [
          {
            accountId: 'ACC001',
            userGroup: 'Premium Clients',
            balance: 100000.00,
            creditLimit: 25000.00,
            floatingPnL: 1630.85,
            equity: 101630.85,
            currency: 'USD',
            usedMargin: 15420.50,
            marginLevel: 658.7,
            marginCall: 100.0,
            stopOut: 50.0
          },
          {
            accountId: 'ACC002',
            userGroup: 'Standard Clients',
            balance: 50000.00,
            creditLimit: 10000.00,
            floatingPnL: -5000.00,
            equity: 45000.00,
            currency: 'USD',
            usedMargin: 60000.00,
            marginLevel: 75.0,
            marginCall: 100.0,
            stopOut: 50.0
          },
          {
            accountId: 'ACC003',
            userGroup: 'Premium Clients',
            balance: 250000.00,
            creditLimit: 50000.00,
            floatingPnL: 3183.38,
            equity: 253183.38,
            currency: 'USD',
            usedMargin: 42680.20,
            marginLevel: 593.5,
            marginCall: 100.0,
            stopOut: 50.0
          },
          {
            accountId: 'ACC004',
            userGroup: 'Standard Clients',
            balance: 25000.00,
            creditLimit: 5000.00,
            floatingPnL: -12500.00,
            equity: 12500.00,
            currency: 'USD',
            usedMargin: 31250.00,
            marginLevel: 40.0,
            marginCall: 100.0,
            stopOut: 50.0
          },
          {
            accountId: 'ACC005',
            userGroup: 'Premium Clients',
            balance: 75000.00,
            creditLimit: 20000.00,
            floatingPnL: 913.01,
            equity: 75913.01,
            currency: 'USD',
            usedMargin: 18540.75,
            marginLevel: 409.3,
            marginCall: 100.0,
            stopOut: 50.0
          },
          {
            accountId: 'ACC006',
            userGroup: 'Standard Clients',
            balance: 80000.00,
            creditLimit: 15000.00,
            floatingPnL: -20000.00,
            equity: 60000.00,
            currency: 'USD',
            usedMargin: 85000.00,
            marginLevel: 70.6,
            marginCall: 100.0,
            stopOut: 50.0
          },
          {
            accountId: 'ACC007',
            userGroup: 'Premium Clients',
            balance: 150000.00,
            creditLimit: 35000.00,
            floatingPnL: -85000.00,
            equity: 65000.00,
            currency: 'USD',
            usedMargin: 130000.00,
            marginLevel: 50.0,
            marginCall: 100.0,
            stopOut: 50.0
          }
        ];
        
        const mockPositionHistory = [
          {
            accountId: 'ACC001',
            positionId: 'POS101',
            symbol: 'EURUSD',
            type: 'BUY',
            volume: 1.0,
            openPrice: 1.0820,
            closePrice: 1.0845,
            finalPL: 250.00,
            openTime: '2024-06-18 14:30:00',
            closeTime: '2024-06-18 16:45:00',
            status: 'CLOSED'
          },
          {
            accountId: 'ACC002',
            positionId: 'POS102',
            symbol: 'GBPUSD',
            type: 'SELL',
            volume: 0.8,
            openPrice: 1.2680,
            closePrice: 1.2640,
            finalPL: 320.00,
            openTime: '2024-06-18 09:15:00',
            closeTime: '2024-06-18 11:30:00',
            status: 'CLOSED'
          },
          {
            accountId: 'ACC003',
            positionId: 'POS103',
            symbol: 'USDJPY',
            type: 'BUY',
            volume: 1.5,
            openPrice: 149.20,
            closePrice: 149.80,
            finalPL: 900.00,
            openTime: '2024-06-17 13:20:00',
            closeTime: '2024-06-17 15:10:00',
            status: 'CLOSED'
          },
          {
            accountId: 'ACC004',
            positionId: 'POS104',
            symbol: 'AUDUSD',
            type: 'SELL',
            volume: 2.0,
            openPrice: 0.6580,
            closePrice: 0.6520,
            finalPL: 1200.00,
            openTime: '2024-06-17 08:45:00',
            closeTime: '2024-06-17 12:20:00',
            status: 'CLOSED'
          },
          {
            accountId: 'ACC005',
            positionId: 'POS105',
            symbol: 'XAUUSD',
            type: 'BUY',
            volume: 0.5,
            openPrice: 2040.50,
            closePrice: 2055.20,
            finalPL: 735.00,
            openTime: '2024-06-16 10:30:00',
            closeTime: '2024-06-16 14:15:00',
            status: 'CLOSED'
          },
          {
            accountId: 'ACC001',
            positionId: 'POS106',
            symbol: 'EURUSD',
            type: 'SELL',
            volume: 1.2,
            openPrice: 1.0890,
            closePrice: 1.0920,
            finalPL: -360.00,
            openTime: '2024-06-16 16:00:00',
            closeTime: '2024-06-16 17:30:00',
            status: 'CLOSED'
          }
        ];

        const mockOrderHistory = [
          {
            accountId: 'ACC001',
            orderId: 'ORD101',
            symbol: 'EURUSD',
            type: 'BUY LIMIT',
            volume: 1.0,
            orderPrice: 1.0780,
            placementTime: '2024-06-18 12:00:00',
            cancelTime: '2024-06-18 18:00:00',
            status: 'CANCELLED'
          },
          {
            accountId: 'ACC002',
            orderId: 'ORD102',
            symbol: 'GBPUSD',
            type: 'SELL STOP',
            volume: 0.5,
            orderPrice: 1.2600,
            placementTime: '2024-06-17 14:30:00',
            cancelTime: '2024-06-17 16:45:00',
            status: 'EXECUTED'
          },
          {
            accountId: 'ACC003',
            orderId: 'ORD103',
            symbol: 'USDJPY',
            type: 'BUY LIMIT',
            volume: 2.0,
            orderPrice: 148.80,
            placementTime: '2024-06-17 11:15:00',
            cancelTime: '2024-06-17 13:20:00',
            status: 'EXECUTED'
          },
          {
            accountId: 'ACC004',
            orderId: 'ORD104',
            symbol: 'AUDUSD',
            type: 'SELL LIMIT',
            volume: 1.5,
            orderPrice: 0.6620,
            placementTime: '2024-06-16 09:30:00',
            cancelTime: '2024-06-16 11:45:00',
            status: 'CANCELLED'
          },
          {
            accountId: 'ACC005',
            orderId: 'ORD105',
            symbol: 'XAUUSD',
            type: 'BUY STOP',
            volume: 0.3,
            orderPrice: 2065.00,
            placementTime: '2024-06-16 15:20:00',
            cancelTime: '2024-06-16 17:30:00',
            status: 'EXPIRED'
          },
          {
            accountId: 'ACC006',
            orderId: 'ORD106',
            symbol: 'USDCAD',
            type: 'SELL LIMIT',
            volume: 0.8,
            orderPrice: 1.3750,
            placementTime: '2024-06-15 13:45:00',
            cancelTime: '2024-06-15 16:20:00',
            status: 'CANCELLED'
          }
        ];
        
        const mockDealHistory: Deal[] = [
          // Balance adjustment
          {
            executionTime: '2023-10-03 06:49:11',
            dealId: '537',
            accountId: 'ACC001',
            type: 'balance',
            commission: 0.00,
            swap: 0.00,
            profitLoss: 0.00,
            balance: 4000.00,
            comment: 'Autotf ********/fr ********'
          },
          // EURGBP sell position opening
          {
            executionTime: '2023-10-04 16:15:00',
            dealId: '538',
            accountId: 'ACC002',
            symbol: 'EURGBP',
            type: 'sell',
            direction: 'in',
            volume: 1.65,
            executionPrice: 0.86756,
            orderId: '320',
            commission: -4.95,
            swap: 0.00,
            profitLoss: 0.00,
            balance: 3995.05
          },
          // EURGBP buy position closing (opposite of sell in)
          {
            executionTime: '2023-10-04 13:00:58',
            dealId: '539',
            accountId: 'ACC002',
            symbol: 'EURGBP',
            type: 'buy',
            direction: 'out',
            volume: 1.65,
            executionPrice: 0.86607,
            orderId: '321',
            commission: -4.95,
            swap: 0.00,
            profitLoss: 298.06,
            balance: 4288.16
          },
          // More deal history entries...
          {
            executionTime: '2025-06-23 10:29:27',
            dealId: 'DEAL567278',
            accountId: 'ACC001',
            symbol: 'EURUSD',
            type: 'buy',
            direction: 'in',
            volume: 0.5,
            executionPrice: 1.08267,
            orderId: 'POS567278',
            commission: -3.50,
            swap: 0.00,
            profitLoss: 0.00,
            balance: 10000.00
          }
        ];
        
        const mockAuditEntries: AuditEntry[] = [
          {
            id: 'AUDIT_1734567890123_abc123',
            timestamp: '2024-06-20T14:30:25.123Z',
            userId: 'DEALER001',
            action: 'DELETE',
            entityType: 'Position',
            entityId: 'POS107',
            accountId: 'ACC003',
            symbol: 'EURUSD',
            details: 'DELETED position history record POS107 - EURUSD BUY 0.5 lots, Final P/L: +$125.50',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567885456_def456',
            timestamp: '2024-06-20T14:25:15.456Z',
            userId: 'DEALER001',
            action: 'EDIT',
            entityType: 'Position',
            entityId: 'POS001',
            accountId: 'ACC001',
            symbol: 'EURUSD',
            details: 'Modified position POS001: Stop Loss changed from 1.0800 to 1.0810, Take Profit changed from 1.0900 to 1.0920',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001',
            changes: [
              { field: 'Stop Loss', from: 1.0800, to: 1.0810 },
              { field: 'Take Profit', from: 1.0900, to: 1.0920 }
            ]
          },
          {
            id: 'AUDIT_1734567880789_ghi789',
            timestamp: '2024-06-20T14:20:45.789Z',
            userId: 'DEALER001',
            action: 'CLOSE',
            entityType: 'Position',
            entityId: 'POS108',
            accountId: 'ACC002',
            symbol: 'GBPUSD',
            details: 'Closed position POS108 - GBPUSD SELL 0.8 lots at 1.2640, P/L: +$320.00',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567875123_jkl012',
            timestamp: '2024-06-20T14:15:30.123Z',
            userId: 'DEALER001',
            action: 'CANCEL',
            entityType: 'Order',
            entityId: 'ORD107',
            accountId: 'ACC004',
            symbol: 'USDJPY',
            details: 'Cancelled order ORD107 - USDJPY BUY LIMIT 1.0 lots at 148.50',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567870456_mno345',
            timestamp: '2024-06-20T14:10:12.456Z',
            userId: 'DEALER001',
            action: 'DELETE',
            entityType: 'Order',
            entityId: 'ORD108',
            accountId: 'ACC005',
            symbol: 'AUDUSD',
            details: 'DELETED order history record ORD108 - AUDUSD SELL LIMIT 1.5 lots at 0.6520',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567865789_pqr678',
            timestamp: '2024-06-20T14:05:45.789Z',
            userId: 'DEALER001',
            action: 'EDIT',
            entityType: 'Order',
            entityId: 'ORD001',
            accountId: 'ACC001',
            symbol: 'EURUSD',
            details: 'Modified order ORD001: Stop Loss changed from 1.0750 to 1.0760',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001',
            changes: [
              { field: 'Stop Loss', from: 1.0750, to: 1.0760 }
            ]
          },
          {
            id: 'AUDIT_1734567860123_stu901',
            timestamp: '2024-06-20T14:00:30.123Z',
            userId: 'DEALER001',
            action: 'CREATE',
            entityType: 'Position',
            entityId: 'POS021',
            accountId: 'ACC006',
            symbol: 'XAUUSD',
            details: 'Created new position POS021 - XAUUSD BUY 0.3 lots at 2052.45',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567855456_vwx234',
            timestamp: '2024-06-20T13:55:15.456Z',
            userId: 'DEALER001',
            action: 'LOGIN',
            entityType: 'System',
            entityId: 'SESSION_1734567890001',
            details: 'User DEALER001 logged into OTX Dealer Terminal Pro from Windows Workstation',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567850789_yza567',
            timestamp: '2024-06-20T13:50:45.789Z',
            userId: 'DEALER002',
            action: 'EDIT',
            entityType: 'Position',
            entityId: 'POS019',
            accountId: 'ACC005',
            symbol: 'EURUSD',
            details: 'Modified position POS019: Take Profit changed from 1.0870 to 1.0880',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890002',
            changes: [
              { field: 'Take Profit', from: 1.0870, to: 1.0880 }
            ]
          },
          {
            id: 'AUDIT_1734567845123_bcd890',
            timestamp: '2024-06-20T13:45:30.123Z',
            userId: 'DEALER002',
            action: 'DELETE',
            entityType: 'Position',
            entityId: 'POS109',
            accountId: 'ACC007',
            symbol: 'GBPJPY',
            details: 'DELETED position history record POS109 - GBPJPY SELL 0.6 lots, Final P/L: -$45.20',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890002'
          },
          {
            id: 'AUDIT_1734567840456_efg123',
            timestamp: '2024-06-20T13:40:15.456Z',
            userId: 'DEALER002',
            action: 'LOGIN',
            entityType: 'System',
            entityId: 'SESSION_1734567890002',
            details: 'User DEALER002 logged into OTX Dealer Terminal Pro from Windows Workstation',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890002'
          },
          {
            id: 'AUDIT_1734567835789_hij456',
            timestamp: '2024-06-20T13:35:45.789Z',
            userId: 'DEALER003',
            action: 'LOGOUT',
            entityType: 'System',
            entityId: 'SESSION_1734567890003',
            details: 'User DEALER003 logged out from OTX Dealer Terminal Pro (Session ended)',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890003'
          },
          {
            id: 'AUDIT_1734567830123_klm789',
            timestamp: '2024-06-20T13:30:30.123Z',
            userId: 'DEALER001',
            action: 'CREATE',
            entityType: 'Order',
            entityId: 'ORD110',
            accountId: 'ACC002',
            symbol: 'EURJPY',
            details: 'Created new order ORD110 - EURJPY BUY LIMIT 0.8 lots at 162.500',
            ipAddress: '*************',
            sessionId: 'SESSION_1734567890001'
          },
          {
            id: 'AUDIT_1734567825456_nop012',
            timestamp: '2024-06-20T13:25:15.456Z',
            userId: 'SYSTEM',
            action: 'CREATE',
            entityType: 'System',
            entityId: 'BACKUP_20240620',
            details: 'Automated daily system backup completed successfully (Database backup created)',
            ipAddress: '127.0.0.1',
            sessionId: 'SYSTEM_PROCESS'
          }
        ];
        
        setQuotes(mockQuotes.sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime()));
        setPositions(mockPositions.sort((a, b) => new Date(b.openTime).getTime() - new Date(a.openTime).getTime()));
        setOrders(mockOrders.sort((a, b) => new Date(b.placementTime).getTime() - new Date(a.placementTime).getTime()));
        setTraderAccounts(mockTraderAccounts);
        setPositionHistory(mockPositionHistory.sort((a, b) => new Date(b.closeTime).getTime() - new Date(a.closeTime).getTime()));
        setOrderHistory(mockOrderHistory.sort((a, b) => new Date(b.cancelTime).getTime() - new Date(a.cancelTime).getTime()));
        setDealHistory(mockDealHistory.sort((a, b) => new Date(b.executionTime).getTime() - new Date(a.executionTime).getTime()));
        setAuditLog(mockAuditEntries.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()));
        
        // Create LOGIN audit record for current session
        const loginAuditEntry: AuditEntry = {
          id: 'AUDIT_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
          timestamp: new Date().toISOString(),
          userId: currentUser.userId,
          action: 'LOGIN',
          entityType: 'System',
          entityId: currentUser.sessionId,
          details: `User ${currentUser.userId} logged into OTX Dealer Terminal Pro from Windows Workstation (Current Session)`,
          ipAddress: currentUser.ipAddress,
          sessionId: currentUser.sessionId
        };
        
        // Add the login record to the top of the audit log
        setAuditLog(prev => [loginAuditEntry, ...prev]);
        
        // Simulate real-time updates
        const updateQuotes = () => {
          setQuotes(prevQuotes => {
            const updatedQuotes = prevQuotes.map(quote => {
              // Price quotes always update regardless of trading status
              
              // Define different volatility ranges based on asset type
              let volatility = 0.0001; // Default forex volatility
              
              if (quote.symbol.includes('XAU') || quote.symbol.includes('XAG')) {
                // Precious metals - medium volatility
                volatility = quote.symbol === 'XAUUSD' ? 0.5 : 0.02;
              } else if (quote.symbol.includes('BTC') || quote.symbol.includes('ETH')) {
                // Cryptocurrencies - high volatility  
                volatility = quote.symbol === 'BTCUSD' ? 25 : 2;
              } else if (quote.symbol.includes('JPY')) {
                // JPY pairs - medium volatility
                volatility = 0.01;
              }
              
              return {
              ...quote,
                bid: quote.bid + (Math.random() - 0.5) * volatility,
                ask: quote.ask + (Math.random() - 0.5) * volatility,
                time: quote.symbol === 'AUDUSD' ? 'STALE_DATA' : new Date().toLocaleTimeString()
              };
            });

            // Update positions with new current prices and recalculate P/L
            setPositions(prevPositions => 
              prevPositions.map(position => {
                const matchingQuote = updatedQuotes.find(q => q.symbol === position.symbol);
                if (matchingQuote) {
                  const currentPrice = position.type === 'BUY' ? matchingQuote.bid : matchingQuote.ask;
                  const priceDiff = position.type === 'BUY' 
                    ? currentPrice - position.openPrice 
                    : position.openPrice - currentPrice;
                  const pl = (priceDiff * position.volume * 100000) + position.swap + position.commission;
                  
                  return {
                    ...position,
                    currentPrice: formatPrice(currentPrice, position.symbol),
                    pl: parseFloat(pl.toFixed(2))
                  };
                }
                return position;
              })
            );

            // Update orders with new current prices
            setOrders(prevOrders => 
              prevOrders.map(order => {
                const matchingQuote = updatedQuotes.find(q => q.symbol === order.symbol);
                if (matchingQuote) {
                  const currentPrice = order.type.includes('BUY') ? matchingQuote.ask : matchingQuote.bid;
                  return {
                    ...order,
                    currentPrice: formatPrice(currentPrice, order.symbol)
                  };
                }
                return order;
              })
            );

            // Update trader accounts with simulated real-time changes
            setTraderAccounts(prevAccounts => 
              prevAccounts.map(account => {
                // Only update equity slightly, keep margin levels static for demo
                let newEquity;
                
                if (account.accountId === 'ACC004' || account.accountId === 'ACC006') {
                  // Stop out accounts - small equity changes only
                  const equityChange = (Math.random() - 0.5) * 50;
                  newEquity = Math.max(account.balance * 0.08, Math.min(account.balance * 0.25, account.equity + equityChange));
                } else if (account.accountId === 'ACC002' || account.accountId === 'ACC007') {
                  // Margin call accounts - small equity changes only
                  const equityChange = (Math.random() - 0.5) * 100;
                  newEquity = Math.max(account.balance * 0.35, Math.min(account.balance * 0.75, account.equity + equityChange));
                } else {
                  // Normal accounts - small equity changes only
                  const equityChange = (Math.random() - 0.5) * 200;
                  newEquity = Math.max(account.balance * 0.85, Math.min(account.balance * 1.05, account.equity + equityChange));
                }
                
                return {
                  ...account,
                  equity: parseFloat(newEquity.toFixed(2))
                  // marginLevel stays static for clear demo effect
                };
              })
            );

            return updatedQuotes;
          });
        };
        
        const interval = setInterval(updateQuotes, 1000);
        
        return () => clearInterval(interval);
      }, 2000);
    };

    connectWebSocket();
  }, []);

  return (
    <WebSocketContext.Provider value={{ 
      connectionStatus, 
      quotes, 
      positions, 
      orders, 
      positionHistory,
      orderHistory,
      dealHistory,
      traderAccounts,
      auditLog,
      tradingDisabledSymbols,
      allTradingDisabled,
      updatePosition,
      updateOrder,
      closePosition,
      cancelOrder,
      deletePositionHistory,
      deleteOrderHistory,
      deleteDealHistory,
      addAuditEntry,
      addPosition,
      addOrder,
      toggleSymbolTradingDisabled,
      toggleAllTradingDisabled,
      updateTradingDisabledSymbols
    }}>
      {children}
    </WebSocketContext.Provider>
  );
};
